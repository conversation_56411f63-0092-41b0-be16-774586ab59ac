package com.ylkj.service.omg;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylkj.model.domain.omg.OComments;
import com.ylkj.model.vo.LikeCheckRequest;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0.0
 * @Project：agtfind-backbend
 * @Package com.ylkj.service.omg
 * @Title: OICommentsService
 * @author: 小许
 * @date: 2025/5/19/周一 10:49
 * @description: omg_商品评价表服务接口
 */
public interface OICommentsService extends IService<OComments> {

    Integer addProductComment(Long productId, OComments commentDTO);

    List<OComments> checkLikeStatus(LikeCheckRequest vo);

    int likeComment(Long commentId, Long userId);

    int unlikeComment(Long commentId, Long userId);

    /**
     * 回复商品评论
     * @param requestData 请求数据
     * @return 回复结果
     */
    int replyComment(Map<String, Object> requestData);
}
