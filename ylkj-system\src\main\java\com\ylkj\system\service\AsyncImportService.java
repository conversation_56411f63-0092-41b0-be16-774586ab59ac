package com.ylkj.system.service;

import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.model.vo.omgProducts.ImportResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 异步导入服务
 * 用于处理大数据量的异步导入任务
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class AsyncImportService {
    
    @Autowired
    private IOmgProductsService omgProductsService;
    
    // 任务状态缓存
    private final Map<String, ImportTaskStatus> taskStatusMap = new ConcurrentHashMap<>();
    
    /**
     * 导入任务状态
     */
    public static class ImportTaskStatus {
        private String taskId;
        private String status; // RUNNING, COMPLETED, FAILED
        private int progress; // 0-100
        private String message;
        private ImportResultVo result;
        private long startTime;
        private long endTime;
        
        // 构造函数
        public ImportTaskStatus(String taskId) {
            this.taskId = taskId;
            this.status = "RUNNING";
            this.progress = 0;
            this.startTime = System.currentTimeMillis();
        }
        
        // Getter and Setter methods
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public int getProgress() { return progress; }
        public void setProgress(int progress) { this.progress = progress; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public ImportResultVo getResult() { return result; }
        public void setResult(ImportResultVo result) { this.result = result; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        
        public long getDuration() {
            return endTime > 0 ? endTime - startTime : System.currentTimeMillis() - startTime;
        }
    }
    
    /**
     * 异步批量导入商品
     * 
     * @param omgProductsList 商品列表
     * @param batchSize 批处理大小
     * @param taskId 任务ID
     * @return 异步任务结果
     */
    @Async("taskExecutor")
    public CompletableFuture<ImportResultVo> importProductsAsync(
            List<OmgProducts> omgProductsList, int batchSize, String taskId) {
        
        ImportTaskStatus taskStatus = new ImportTaskStatus(taskId);
        taskStatusMap.put(taskId, taskStatus);
        
        try {
            System.out.println("开始异步导入任务: " + taskId);
            taskStatus.setMessage("正在导入商品...");
            
            // 执行批量导入
            ImportResultVo result = omgProductsService.importOmgProductsBatch(omgProductsList, batchSize);
            
            // 更新任务状态
            taskStatus.setStatus("COMPLETED");
            taskStatus.setProgress(100);
            taskStatus.setResult(result);
            taskStatus.setEndTime(System.currentTimeMillis());
            taskStatus.setMessage("导入完成");
            
            System.out.println("异步导入任务完成: " + taskId);
            return CompletableFuture.completedFuture(result);
            
        } catch (Exception e) {
            // 更新任务状态为失败
            taskStatus.setStatus("FAILED");
            taskStatus.setEndTime(System.currentTimeMillis());
            taskStatus.setMessage("导入失败: " + e.getMessage());
            
            System.err.println("异步导入任务失败: " + taskId + ", 错误: " + e.getMessage());
            e.printStackTrace();
            
            // 创建失败结果
            ImportResultVo failedResult = new ImportResultVo();
            failedResult.setTotalCount(omgProductsList.size());
            failedResult.setFailedCount(omgProductsList.size());
            failedResult.setMessage("异步导入失败: " + e.getMessage());
            
            return CompletableFuture.completedFuture(failedResult);
        }
    }
    
    /**
     * 获取任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    public ImportTaskStatus getTaskStatus(String taskId) {
        return taskStatusMap.get(taskId);
    }
    
    /**
     * 获取所有任务状态
     * 
     * @return 所有任务状态
     */
    public Map<String, ImportTaskStatus> getAllTaskStatus() {
        return new ConcurrentHashMap<>(taskStatusMap);
    }
    
    /**
     * 清理已完成的任务状态
     * 
     * @param maxAge 最大保留时间（毫秒）
     */
    public void cleanupCompletedTasks(long maxAge) {
        long currentTime = System.currentTimeMillis();
        taskStatusMap.entrySet().removeIf(entry -> {
            ImportTaskStatus status = entry.getValue();
            return ("COMPLETED".equals(status.getStatus()) || "FAILED".equals(status.getStatus()))
                    && (currentTime - status.getEndTime()) > maxAge;
        });
    }
    
    /**
     * 生成任务ID
     * 
     * @return 唯一任务ID
     */
    public String generateTaskId() {
        return "async_import_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
    
    /**
     * 取消任务（注意：已经开始的导入无法真正取消，只是标记状态）
     * 
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    public boolean cancelTask(String taskId) {
        ImportTaskStatus status = taskStatusMap.get(taskId);
        if (status != null && "RUNNING".equals(status.getStatus())) {
            status.setStatus("CANCELLED");
            status.setEndTime(System.currentTimeMillis());
            status.setMessage("任务已取消");
            return true;
        }
        return false;
    }
}
