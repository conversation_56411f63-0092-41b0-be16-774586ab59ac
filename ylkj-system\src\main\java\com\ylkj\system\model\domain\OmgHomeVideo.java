package com.ylkj.system.model.domain;

import java.io.Serializable;
import java.util.Map;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.ylkj.common.enums.VideoTypeEnum;
import lombok.Data;
import com.ylkj.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
/**
 * omg首页视频组对象 omg_home_video
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@TableName("omg_home_video")
@Data
public class OmgHomeVideo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 达人视频表id */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /** 视频URL */
    @Excel(name = "视频URL")
    private String videoUrl;

    /** 头像 */
    @Excel(name = "头像")
    private String avatar;

    /** 名称 */
    @Excel(name = "名称")
    private String username;

    /** 内容 */
    @Excel(name = "内容")
    private String description;

    /** 视频类型 */
    @Excel(name = "视频类型")
    private VideoTypeEnum type;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;
}
