<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.system.mapper.OmgProductsMapper">

    <resultMap type="OmgProducts" id="OmgProductsResult">
        <result property="productId" column="product_id"/>
        <result property="sellerId" column="seller_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="name" column="product_name"/>
        <result property="slug" column="product_slug"/>
        <result property="description" column="product_description"/>
        <result property="price" column="price"/>
        <result property="originalPrice" column="original_price"/>
        <result property="mainImage" column="main_image"/>
        <result property="sku" column="sku"/>
        <result property="stock" column="stock"/>
        <result property="likes" column="likes"/>
        <result property="views" column="views"/>
        <result property="virtualViews" column="virtual_views"/>
        <result property="rating" column="rating"/>
        <result property="totalRatings" column="total_ratings"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="merchant" column="merchant"/>
        <result property="platform" column="platform"/>
        <result property="tag" column="tag"/>
        <result property="fromUrl" column="from_url"/>
        <result property="productStatus" column="product_status"/>
        <result property="qc" column="qc"/>
        <result property="video" column="video"/>
        <result property="averageArrival" column="average_arrival"/>
        <result property="weight" column="weight"/>
        <result property="productAttributes" column="product_attributes"/>
        <!-- 分类信息映射 -->
        <result property="categoryName" column="category_name"/>
        <!-- 一对一映射品牌信息 -->
        <association property="OmgBrand" javaType="OmgBrands">
            <id property="brandId" column="brand_id"/>
            <result property="name" column="brand_name"/>
            <result property="slug" column="brand_slug"/>
            <result property="logoUrl" column="logo_url"/>
            <result property="description" column="brand_description"/>
            <result property="parentCategoryId" column="parent_category_id"/>
        </association>
    </resultMap>



    <sql id="selectOmgProductsVo">
        select product_id,
               seller_id,
               category_id,
               name as product_name,
               slug as product_slug,
               description as product_description,
               price,
               original_price,
               main_image,
               sku,
               stock,
               likes,
               views,
               virtual_views,
               rating,
               total_ratings,
               status,
               created_at,
               updated_at,
               merchant,
               platform,
               video,
               average_arrival,
               weight,
               product_attributes
        from omg_products
    </sql>
    <sql id="selectOmgProductsWithBrand">
        select p.product_id,
               p.seller_id,
               p.category_id,
               p.name        as product_name,
               p.slug        as product_slug,
               p.description as product_description,
               p.price,
               p.original_price,
               p.main_image,
               p.sku,
               p.stock,
               p.likes,
               p.views,
               p.virtual_views,
               p.rating,
               p.total_ratings,
               p.status,
               p.created_at,
               p.updated_at,
               p.merchant,
               p.platform,
               p.tag,
               p.from_url,
               p.product_status,
               p.qc,
               p.video,
               p.average_arrival,
               p.weight,
               p.product_attributes,
               b.brand_id,
               b.name        as brand_name,
               b.slug        as brand_slug,
               b.logo_url,
               b.parent_category_id,
               b.description as brand_description,
               c.name        as category_name
        from omg_products p
                 left join omg_product_brands pb on p.product_id = pb.product_id
                 left join omg_brands b on pb.brand_id = b.brand_id
                 left join omg_categories c on p.category_id = c.category_id
    </sql>

    <select id="selectOmgProductsList" parameterType="OmgProducts" resultMap="OmgProductsResult">
        <include refid="selectOmgProductsWithBrand"/>
        <where>
            <if test="name != null  and name != ''">and p.name like concat('%', #{name}, '%')</if>
            <if test="slug != null  and slug != ''">and p.slug like concat('%', #{slug}, '%')</if>
            <if test="merchant != null  and merchant != ''">and p.merchant like concat('%', #{merchant}, '%')</if>
            <!-- 价格区间筛选：支持最低价格筛选 -->
            <if test="minPrice != null and minPrice != ''">and p.price &gt;= #{minPrice}</if>
            <!-- 价格区间筛选：支持最高价格筛选 -->
            <if test="maxPrice != null and maxPrice != ''">and p.price &lt;= #{maxPrice}</if>
            <if test="status != null  and status != ''">and p.status = #{status}</if>
            <if test="platform != null  and platform != ''">and p.platform = #{platform}</if>
        </where>
    </select>

    <select id="selectOmgProductsByProductId" parameterType="Long" resultMap="OmgProductsResult">
        <include refid="selectOmgProductsWithBrand"/>
        where p.product_id = #{productId}
    </select>

    <insert id="insertOmgProducts" parameterType="OmgProducts" useGeneratedKeys="true" keyProperty="productId">
        insert into omg_products
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sellerId != null">seller_id,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="slug != null and slug != ''">slug,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="price != null">price,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="mainImage != null and mainImage != ''">main_image,</if>
            <if test="sku != null and sku != ''">sku,</if>
            <if test="stock != null">stock,</if>
            <if test="likes != null">likes,</if>
            <if test="views != null">views,</if>
            <if test="virtualViews != null">virtual_views,</if>
            <if test="rating != null">rating,</if>
            <if test="totalRatings != null">total_ratings,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="merchant != null and merchant != ''">merchant,</if>
            <if test="platform != null and platform != ''">platform,</if>
            <if test="storeName != null and storeName != ''">store_name,</if>
            <if test="productStatus != null and productStatus !=''">product_status,</if>
            <if test="tag != null and tag != ''">tag,</if>
            <if test="fromUrl != null and fromUrl != ''">from_url,</if>
            <if test="qc != null and qc != ''">qc,</if>
            <if test="video != null and video != ''">video,</if>
            <if test="averageArrival != null and averageArrival != ''">average_arrival,</if>
            <if test="weight != null">weight,</if>
            <if test="productAttributes != null and productAttributes != ''">product_attributes,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sellerId != null">#{sellerId},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="slug != null and slug != ''">#{slug},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="price != null">#{price},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="mainImage != null and mainImage != ''">#{mainImage},</if>
            <if test="sku != null and sku != ''">#{sku},</if>
            <if test="stock != null">#{stock},</if>
            <if test="likes != null">#{likes},</if>
            <if test="views != null">#{views},</if>
            <if test="virtualViews != null">#{virtualViews},</if>
            <if test="rating != null">#{rating},</if>
            <if test="totalRatings != null">#{totalRatings},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="merchant != null and merchant != ''">#{merchant},</if>
            <if test="platform != null and platform != ''">#{platform},</if>
            <if test="storeName != null and storeName != ''">#{storeName},</if>
            <if test="productStatus !=null and productStatus !=''">#{productStatus},</if>
            <if test="tag != null and tag != ''">#{tag},</if>
            <if test="fromUrl != null and fromUrl != ''">#{fromUrl},</if>
            <if test="qc != null and qc != ''">#{qc},</if>
            <if test="video != null and video != ''">#{video},</if>
            <if test="averageArrival != null and averageArrival != ''">#{averageArrival},</if>
            <if test="weight != null">#{weight},</if>
            <if test="productAttributes != null and productAttributes != ''">#{productAttributes},</if>
        </trim>
    </insert>
    <insert id="insertOmgProductRelativeBrand" parameterType="OmgProducts">
        insert into omg_product_brands (product_id, brand_id)
        values (#{productId}, #{brandId})
    </insert>

    <update id="updateOmgProducts" parameterType="OmgProducts">
        update omg_products
        <trim prefix="SET" suffixOverrides=",">
            <if test="sellerId != null">seller_id = #{sellerId},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="slug != null and slug != ''">slug = #{slug},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="price != null">price = #{price},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="mainImage != null and mainImage != ''">main_image = #{mainImage},</if>
            <if test="sku != null and sku != ''">sku = #{sku},</if>
            <if test="stock != null">stock = #{stock},</if>
            <if test="likes != null">likes = #{likes},</if>
            <if test="views != null">views = #{views},</if>
            <if test="virtualViews != null">virtual_views = #{virtualViews},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="totalRatings != null">total_ratings = #{totalRatings},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="merchant != null and merchant != ''">merchant = #{merchant},</if>
            <if test="platform != null and platform != ''">platform = #{platform},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="tag != null">tag = #{tag},</if>
            <if test="fromUrl != null">from_url = #{fromUrl},</if>
            <if test="productStatus != null">product_status = #{productStatus},</if>
            <if test="qc != null">qc = #{qc},</if>
            <if test="video != null">video = #{video},</if>
            <if test="averageArrival != null">average_arrival = #{averageArrival},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="productAttributes != null and productAttributes != ''">product_attributes = #{productAttributes},</if>
        </trim>
        where product_id = #{productId}
    </update>
    <update id="updateOmgProductsStatus" parameterType="com.ylkj.system.model.vo.agtProducts.UpdateStatusVo">
        update omg_products
        set status = #{status}
        where product_id = #{productId}
    </update>

    <delete id="deleteOmgProductsByProductId" parameterType="Long">
        delete
        from omg_products
        where product_id = #{productId}
    </delete>

    <delete id="deleteOmgProductsByProductIds" parameterType="String">
        delete from omg_products where product_id in
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>
    <delete id="deleteOmgProductRelativeBrandByProductId" parameterType="java.lang.Long">
        delete from omg_product_brands where product_id = #{productId}
    </delete>

    <!-- 更新淘宝商品链接 -->
    <update id="updateTaobaoProductsUrl">
        UPDATE omg_products
        SET from_url = CONCAT('https://item.taobao.com/item.htm?id=', sku)
        WHERE (platform LIKE '%taobao%' OR platform LIKE '%淘宝%')
        AND sku IS NOT NULL AND sku != ''
    </update>

    <!-- 更新1688商品链接 -->
    <update id="update1688ProductsUrl">
        UPDATE omg_products
        SET from_url = CONCAT('https://detail.1688.com/offer/', sku, '.html')
        WHERE platform LIKE '%1688%'
        AND sku IS NOT NULL AND sku != ''
    </update>

    <!-- 更新微店商品链接 -->
    <update id="updateWeidianProductsUrl">
        UPDATE omg_products
        SET from_url = CONCAT('https://weidian.com/item.html?itemID=', sku)
        WHERE (platform LIKE '%weidian%' OR platform LIKE '%微店%')
        AND sku IS NOT NULL AND sku != ''
    </update>

    <!-- 根据SKU查询商品 -->
    <select id="selectOmgProductsBySku" parameterType="String" resultMap="OmgProductsResult">
        <include refid="selectOmgProductsWithBrand"/>
        WHERE p.sku = #{sku}
        LIMIT 1
    </select>

    <!-- 获取所有商品的SKU列表 -->
    <select id="getAllProductSkus" resultType="String">
        SELECT DISTINCT sku
        FROM omg_products
        WHERE sku IS NOT NULL AND sku != ''
        ORDER BY sku
    </select>

    <!-- 获取所有商品的SKU和平台信息 -->
    <select id="selectAllProductsWithSkuAndPlatform" resultMap="OmgProductsResult">
        SELECT product_id, sku, platform, main_image, created_at, updated_at
        FROM omg_products
        WHERE sku IS NOT NULL AND sku != '' AND platform IS NOT NULL AND platform != ''
        ORDER BY platform, sku
    </select>

    <!-- 根据平台获取商品列表 -->
    <select id="selectProductsByPlatform" parameterType="String" resultMap="OmgProductsResult">
        SELECT product_id, sku, platform, main_image, created_at, updated_at
        FROM omg_products
        WHERE platform = #{platform} AND sku IS NOT NULL AND sku != ''
        ORDER BY sku
    </select>

    <!-- 批量插入商品 -->
    <insert id="batchInsertOmgProducts" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="productId">
        INSERT INTO omg_products (
            seller_id, category_id, name, slug, description, price, original_price,
            main_image, sku, stock, likes, views, rating, total_ratings, status,
            merchant, platform, product_status, average_arrival, weight, product_attributes,
            created_at, updated_at
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.sellerId}, #{item.categoryId}, #{item.name}, #{item.slug}, #{item.description},
                #{item.price}, #{item.originalPrice}, #{item.mainImage}, #{item.sku}, #{item.stock},
                #{item.likes}, #{item.views}, #{item.rating}, #{item.totalRatings}, #{item.status},
                #{item.merchant}, #{item.platform}, #{item.productStatus}, #{item.averageArrival},
                #{item.weight}, #{item.productAttributes}, NOW(), NOW()
            )
        </foreach>
    </insert>

    <!-- 批量插入商品品牌关联 -->
    <insert id="batchInsertOmgProductRelativeBrand" parameterType="java.util.List">
        INSERT INTO omg_product_brands (product_id, brand_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.productId}, #{item.brandId})
        </foreach>
    </insert>

    <!-- 批量检查SKU是否存在 -->
    <select id="batchCheckSkuExists" resultType="String">
        SELECT DISTINCT sku
        FROM omg_products
        WHERE sku IN
        <foreach collection="skuList" item="sku" open="(" separator="," close=")">
            #{sku}
        </foreach>
    </select>
</mapper>