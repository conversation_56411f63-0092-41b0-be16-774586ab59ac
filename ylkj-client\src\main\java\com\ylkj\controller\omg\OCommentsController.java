package com.ylkj.controller.omg;

import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.model.domain.Comments;
import com.ylkj.model.domain.omg.OComments;
import com.ylkj.model.vo.LikeCheckRequest;
import com.ylkj.service.omg.OICommentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0.0
 * @Project：agtfind-backbend
 * @Package com.ylkj.controller.omg
 * @Title: OCommentsController
 * @author: 小许
 * @date: 2025/5/19/周一 10:47
 * @description: omg_商品评论表
 */
@RestController
@RequestMapping("/front/omg/comments/")
public class OCommentsController {
    @Autowired
    private OICommentsService oICommentsService;

    /**
     * @author: 小许
     * @date: 2025/5/19/周一 10:55
     * @description:
     * @param productId
     * @param commentDTO
     * @return AjaxResult
     */
    @PostMapping("/{productId}/add")
    public AjaxResult addProductComment(
            @PathVariable("productId") Long productId,
            @RequestBody @Validated OComments commentDTO) {
        try {
            // 调用Service层添加评论
            Integer i = oICommentsService.addProductComment(productId, commentDTO);
            if (i == null) {
                return AjaxResult.error("添加评论失败");
            }

            return AjaxResult.success("添加评论成功");

        } catch (Exception e) {

            return AjaxResult.error("添加评论失败");
        }
    }

    /**
     * @author: 小许
     * @date: 2025/5/19/周一 11:26
     * @description: 根据评论id删除评论
     * @param commentId
     * @return AjaxResult
     */
    @RequestMapping("{commentId}")
    public AjaxResult deleteComment(@PathVariable("commentId") Long commentId) {

        if (oICommentsService.removeById(commentId)) {
            return AjaxResult.success("删除成功");
        } else {
            return AjaxResult.error("删除失败");
        }
    }

    /**
     * @author: 小许
     * @date: 2025/5/19/周一 15:51
     * @description: 查询是否点赞
     * @return AjaxResult
     */
    @RequestMapping("/check-like-status")
    public AjaxResult checkLikeStatus(@RequestBody LikeCheckRequest vo){
        return AjaxResult.success(oICommentsService.checkLikeStatus(vo));
    }

    /**
     * @author: 小许
     * @date: 2025/5/19/周一 17:01
     * @description: 评论点赞
     * @param commentId
     * @param userId
     * @return AjaxResult
     */
    @RequestMapping("/{commentId}/like/{userId}")
    public AjaxResult likeComment(@PathVariable("commentId") Long commentId,@PathVariable("userId") Long userId) {
        if (oICommentsService.likeComment(commentId, userId) > 0) {
            return AjaxResult.success("点赞成功");
        } else {
            return AjaxResult.error("点赞失败");
        }
    }


    /**
     * @author: 小许
     * @date: 2025/5/19/周一 17:33
     * @description: 取消点赞
     * @param commentId
     * @param userId
     * @return AjaxResult
     */
    @RequestMapping("/{commentId}/unlike/{userId}")
    public AjaxResult unlikeComment(@PathVariable("commentId") Long commentId,@PathVariable("userId") Long userId) {

        if (oICommentsService.unlikeComment(commentId,userId) > 0) {
            return AjaxResult.success("取消点赞成功");
        } else {
            return AjaxResult.error("取消点赞失败");
        }
    }

    /**
     * 回复商品评论
     */
    @PostMapping("/reply")
    public AjaxResult replyComment(@RequestBody Map<String, Object> requestData) {
        return AjaxResult.success(oICommentsService.replyComment(requestData));
    }
}
