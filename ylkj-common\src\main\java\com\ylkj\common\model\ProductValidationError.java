package com.ylkj.common.model;

import java.util.List;

/**
 * 商品验证错误
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class ProductValidationError {
    
    /** 商品在列表中的索引 */
    private int index;
    
    /** 商品标识（如SKU） */
    private String identifier;
    
    /** 错误信息列表 */
    private List<String> errors;
    
    /** 错误发生时间 */
    private long timestamp;
    
    /** 错误级别 */
    private ErrorLevel level;
    
    /**
     * 错误级别枚举
     */
    public enum ErrorLevel {
        LOW("低"),
        MEDIUM("中"),
        HIGH("高"),
        CRITICAL("严重");
        
        private final String description;
        
        ErrorLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public ProductValidationError() {
        this.timestamp = System.currentTimeMillis();
        this.level = ErrorLevel.MEDIUM;
    }
    
    public ProductValidationError(int index, String identifier, List<String> errors) {
        this();
        this.index = index;
        this.identifier = identifier;
        this.errors = errors;
        this.level = determineErrorLevel(errors);
    }
    
    public ProductValidationError(int index, String identifier, List<String> errors, ErrorLevel level) {
        this();
        this.index = index;
        this.identifier = identifier;
        this.errors = errors;
        this.level = level;
    }
    
    /**
     * 根据错误信息确定错误级别
     * 
     * @param errors 错误信息列表
     * @return 错误级别
     */
    private ErrorLevel determineErrorLevel(List<String> errors) {
        if (errors == null || errors.isEmpty()) {
            return ErrorLevel.LOW;
        }
        
        // 根据错误内容判断级别
        for (String error : errors) {
            String lowerError = error.toLowerCase();
            
            // 严重错误关键词
            if (lowerError.contains("不能为空") || lowerError.contains("必填") || 
                lowerError.contains("格式错误") || lowerError.contains("无效")) {
                return ErrorLevel.HIGH;
            }
            
            // 中等错误关键词
            if (lowerError.contains("长度") || lowerError.contains("范围") || 
                lowerError.contains("超过") || lowerError.contains("小于")) {
                return ErrorLevel.MEDIUM;
            }
        }
        
        return ErrorLevel.LOW;
    }
    
    /**
     * 获取错误摘要
     * 
     * @return 错误摘要
     */
    public String getErrorSummary() {
        if (errors == null || errors.isEmpty()) {
            return "无错误信息";
        }
        
        if (errors.size() == 1) {
            return errors.get(0);
        }
        
        return errors.get(0) + " 等 " + errors.size() + " 个错误";
    }
    
    /**
     * 获取所有错误信息的字符串表示
     * 
     * @param separator 分隔符
     * @return 错误信息字符串
     */
    public String getErrorsAsString(String separator) {
        if (errors == null || errors.isEmpty()) {
            return "";
        }
        return String.join(separator, errors);
    }
    
    /**
     * 是否为严重错误
     * 
     * @return true-严重错误，false-非严重错误
     */
    public boolean isCritical() {
        return level == ErrorLevel.CRITICAL || level == ErrorLevel.HIGH;
    }
    
    /**
     * 获取错误数量
     * 
     * @return 错误数量
     */
    public int getErrorCount() {
        return errors != null ? errors.size() : 0;
    }
    
    // Getter and Setter methods
    public int getIndex() {
        return index;
    }
    
    public void setIndex(int index) {
        this.index = index;
    }
    
    public String getIdentifier() {
        return identifier;
    }
    
    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }
    
    public List<String> getErrors() {
        return errors;
    }
    
    public void setErrors(List<String> errors) {
        this.errors = errors;
        // 重新确定错误级别
        this.level = determineErrorLevel(errors);
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public ErrorLevel getLevel() {
        return level;
    }
    
    public void setLevel(ErrorLevel level) {
        this.level = level;
    }
    
    @Override
    public String toString() {
        return "ProductValidationError{" +
                "index=" + index +
                ", identifier='" + identifier + '\'' +
                ", errorCount=" + getErrorCount() +
                ", level=" + level.getDescription() +
                ", summary='" + getErrorSummary() + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        ProductValidationError that = (ProductValidationError) o;
        
        if (index != that.index) return false;
        return identifier != null ? identifier.equals(that.identifier) : that.identifier == null;
    }
    
    @Override
    public int hashCode() {
        int result = index;
        result = 31 * result + (identifier != null ? identifier.hashCode() : 0);
        return result;
    }
}
