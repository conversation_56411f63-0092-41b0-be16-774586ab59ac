package com.ylkj.controller.omg;

import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.model.vo.OmgHomeVideoClientVO;
import com.ylkj.service.omg.IOmgHomeVideoClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 首页视频控制器（客户端）
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/front/home/<USER>")
public class OmgHomeVideoClientController {
    
    private static final Logger logger = LoggerFactory.getLogger(OmgHomeVideoClientController.class);
    
    @Autowired
    private IOmgHomeVideoClientService homeVideoService;
    
    /**
     * 获取首页视频列表
     *
     * @return 视频列表数据
     */
    @GetMapping("/list")
    public AjaxResult getHomeVideoList() {
        try {
            logger.info("请求获取首页视频列表");
            List<OmgHomeVideoClientVO> videoList = homeVideoService.getHomeVideoList();
            return AjaxResult.success(videoList);
        } catch (Exception e) {
            logger.error("获取首页视频列表失败", e);
            return AjaxResult.error("获取首页视频列表失败");
        }
    }

    /**
     * 根据类型获取首页视频列表
     */
    @GetMapping("/listByType")
    public AjaxResult getHomeVideoListByType(@RequestParam("type") String type) {
        return AjaxResult.success(homeVideoService.getHomeVideoListByType(type));
    }

}