package com.ylkj.system.model.dto;

/**
 * 分类名称到ID的映射DTO
 * 用于批量查询分类ID时的结果映射
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public class CategoryNameIdMapping {
    
    /** 分类名称 */
    private String categoryName;
    
    /** 分类ID */
    private Long categoryId;
    
    /**
     * 默认构造函数
     */
    public CategoryNameIdMapping() {
    }
    
    /**
     * 带参构造函数
     * 
     * @param categoryName 分类名称
     * @param categoryId 分类ID
     */
    public CategoryNameIdMapping(String categoryName, Long categoryId) {
        this.categoryName = categoryName;
        this.categoryId = categoryId;
    }
    
    /**
     * 获取分类名称
     * 
     * @return 分类名称
     */
    public String getCategoryName() {
        return categoryName;
    }
    
    /**
     * 设置分类名称
     * 
     * @param categoryName 分类名称
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }
    
    /**
     * 获取分类ID
     * 
     * @return 分类ID
     */
    public Long getCategoryId() {
        return categoryId;
    }
    
    /**
     * 设置分类ID
     * 
     * @param categoryId 分类ID
     */
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    
    @Override
    public String toString() {
        return "CategoryNameIdMapping{" +
                "categoryName='" + categoryName + '\'' +
                ", categoryId=" + categoryId +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        CategoryNameIdMapping that = (CategoryNameIdMapping) o;
        
        if (categoryName != null ? !categoryName.equals(that.categoryName) : that.categoryName != null) return false;
        return categoryId != null ? categoryId.equals(that.categoryId) : that.categoryId == null;
    }
    
    @Override
    public int hashCode() {
        int result = categoryName != null ? categoryName.hashCode() : 0;
        result = 31 * result + (categoryId != null ? categoryId.hashCode() : 0);
        return result;
    }
}
