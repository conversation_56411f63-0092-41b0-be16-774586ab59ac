package com.ylkj.system.model.vo.omgHomeVideo;

import java.io.Serializable;
import java.util.Date;

import com.ylkj.common.enums.VideoTypeEnum;
import lombok.Data;
import com.ylkj.common.annotation.Excel;

import org.springframework.beans.BeanUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylkj.system.model.domain.OmgHomeVideo;
/**
 * omg首页视频组Vo对象 omg_home_video
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Data
public class OmgHomeVideoVo implements Serializable
{
    private static final long serialVersionUID = 1L;
    private Long id;
    /** 视频URL */
    @Excel(name = "视频URL")
    private String videoUrl;

    /** 头像 */
    @Excel(name = "头像")
    private String avatar;

    /** 名称 */
    @Excel(name = "名称")
    private String username;

    /** 内容 */
    @Excel(name = "内容")
    private String description;

    /** 视频类型 */
    @Excel(name = "视频类型")
    private VideoTypeEnum type;

     /**
     * 对象转封装类
     *
     * @param omgHomeVideo OmgHomeVideo实体对象
     * @return OmgHomeVideoVo
     */
    public static OmgHomeVideoVo objToVo(OmgHomeVideo omgHomeVideo) {
        if (omgHomeVideo == null) {
            return null;
        }
        OmgHomeVideoVo omgHomeVideoVo = new OmgHomeVideoVo();
        BeanUtils.copyProperties(omgHomeVideo, omgHomeVideoVo);
        return omgHomeVideoVo;
    }
}
