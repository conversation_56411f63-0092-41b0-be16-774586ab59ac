package com.ylkj.system.model.dto;

/**
 * 品牌查询请求DTO
 * 用于批量查询品牌时的请求参数
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public class BrandQueryRequest {
    
    /** 品牌名称 */
    private String brandName;
    
    /** 分类ID */
    private Long categoryId;
    
    /**
     * 默认构造函数
     */
    public BrandQueryRequest() {
    }
    
    /**
     * 带参构造函数
     * 
     * @param brandName 品牌名称
     * @param categoryId 分类ID
     */
    public BrandQueryRequest(String brandName, Long categoryId) {
        this.brandName = brandName;
        this.categoryId = categoryId;
    }
    
    /**
     * 获取品牌名称
     * 
     * @return 品牌名称
     */
    public String getBrandName() {
        return brandName;
    }
    
    /**
     * 设置品牌名称
     * 
     * @param brandName 品牌名称
     */
    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }
    
    /**
     * 获取分类ID
     * 
     * @return 分类ID
     */
    public Long getCategoryId() {
        return categoryId;
    }
    
    /**
     * 设置分类ID
     * 
     * @param categoryId 分类ID
     */
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    
    @Override
    public String toString() {
        return "BrandQueryRequest{" +
                "brandName='" + brandName + '\'' +
                ", categoryId=" + categoryId +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        BrandQueryRequest that = (BrandQueryRequest) o;
        
        if (brandName != null ? !brandName.equals(that.brandName) : that.brandName != null) return false;
        return categoryId != null ? categoryId.equals(that.categoryId) : that.categoryId == null;
    }
    
    @Override
    public int hashCode() {
        int result = brandName != null ? brandName.hashCode() : 0;
        result = 31 * result + (categoryId != null ? categoryId.hashCode() : 0);
        return result;
    }
}
