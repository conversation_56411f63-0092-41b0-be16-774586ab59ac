package com.ylkj.system.model.dto.omgHomeVideo;

import java.util.Map;
import java.io.Serializable;

import com.ylkj.common.enums.VideoTypeEnum;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ylkj.system.model.domain.OmgHomeVideo;
/**
 * omg首页视频组Query对象 omg_home_video
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Data
public class OmgHomeVideoQuery implements Serializable
{
    private static final long serialVersionUID = 1L;
    private Long id;
    /** 视频URL */
    private String videoUrl;

    /** 头像 */
    private String avatar;

    /** 名称 */
    private String username;

    /** 内容 */
    private String description;

    /** 视频类型 */
    private VideoTypeEnum type;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;

    /**
     * 对象转封装类
     *
     * @param omgHomeVideoQuery 查询对象
     * @return OmgHomeVideo
     */
    public static OmgHomeVideo queryToObj(OmgHomeVideoQuery omgHomeVideoQuery) {
        if (omgHomeVideoQuery == null) {
            return null;
        }
        OmgHomeVideo omgHomeVideo = new OmgHomeVideo();
        BeanUtils.copyProperties(omgHomeVideoQuery, omgHomeVideo);
        return omgHomeVideo;
    }
}
