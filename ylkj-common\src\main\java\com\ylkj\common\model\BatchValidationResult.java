package com.ylkj.common.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 批量验证结果
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class BatchValidationResult {
    
    /** 总数量 */
    private int totalCount;
    
    /** 有效数量 */
    private int validCount;
    
    /** 无效数量 */
    private int invalidCount;
    
    /** 全局错误信息 */
    private List<String> globalErrors;
    
    /** 商品验证错误列表 */
    private List<ProductValidationError> productErrors;
    
    /** 商品验证警告列表 */
    private List<ProductValidationWarning> productWarnings;
    
    /** 验证开始时间 */
    private long startTime;
    
    /** 验证结束时间 */
    private long endTime;
    
    public BatchValidationResult() {
        this.globalErrors = new ArrayList<>();
        this.productErrors = new ArrayList<>();
        this.productWarnings = new ArrayList<>();
        this.startTime = System.currentTimeMillis();
    }
    
    /**
     * 添加全局错误
     * 
     * @param error 错误信息
     */
    public void addGlobalError(String error) {
        this.globalErrors.add(error);
    }
    
    /**
     * 添加有效商品
     */
    public void addValidProduct() {
        this.validCount++;
        this.totalCount++;
    }
    
    /**
     * 添加无效商品
     * 
     * @param index 商品索引
     * @param identifier 商品标识（如SKU）
     * @param result 验证结果
     */
    public void addInvalidProduct(int index, String identifier, ValidationResult result) {
        this.invalidCount++;
        this.totalCount++;
        this.productErrors.add(new ProductValidationError(index, identifier, result.getErrors()));
    }
    
    /**
     * 添加警告信息
     * 
     * @param index 商品索引
     * @param identifier 商品标识（如SKU）
     * @param warnings 警告列表
     */
    public void addWarnings(int index, String identifier, List<String> warnings) {
        if (warnings != null && !warnings.isEmpty()) {
            this.productWarnings.add(new ProductValidationWarning(index, identifier, warnings));
        }
    }
    
    /**
     * 完成验证
     */
    public void finish() {
        this.endTime = System.currentTimeMillis();
    }
    
    /**
     * 获取验证耗时
     * 
     * @return 耗时（毫秒）
     */
    public long getDuration() {
        if (endTime > 0) {
            return endTime - startTime;
        }
        return System.currentTimeMillis() - startTime;
    }
    
    /**
     * 计算有效率
     * 
     * @return 有效率（0-100）
     */
    public double getValidRate() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) validCount / totalCount * 100;
    }
    
    /**
     * 计算无效率
     * 
     * @return 无效率（0-100）
     */
    public double getInvalidRate() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) invalidCount / totalCount * 100;
    }
    
    /**
     * 获取验证摘要
     * 
     * @return 验证摘要信息
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("批量验证完成：");
        sb.append("总计 ").append(totalCount).append(" 条，");
        sb.append("有效 ").append(validCount).append(" 条，");
        sb.append("无效 ").append(invalidCount).append(" 条，");
        sb.append("有效率 ").append(String.format("%.2f", getValidRate())).append("%，");
        sb.append("耗时 ").append(getDuration()).append("ms");
        
        if (!globalErrors.isEmpty()) {
            sb.append("，存在全局错误 ").append(globalErrors.size()).append(" 个");
        }
        
        if (!productWarnings.isEmpty()) {
            sb.append("，存在警告 ").append(productWarnings.size()).append(" 个");
        }
        
        return sb.toString();
    }
    
    // Getter and Setter methods
    public int getTotalCount() {
        return totalCount;
    }
    
    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }
    
    public int getValidCount() {
        return validCount;
    }
    
    public void setValidCount(int validCount) {
        this.validCount = validCount;
    }
    
    public int getInvalidCount() {
        return invalidCount;
    }
    
    public void setInvalidCount(int invalidCount) {
        this.invalidCount = invalidCount;
    }
    
    public List<String> getGlobalErrors() {
        return globalErrors;
    }
    
    public void setGlobalErrors(List<String> globalErrors) {
        this.globalErrors = globalErrors;
    }
    
    public List<ProductValidationError> getProductErrors() {
        return productErrors;
    }
    
    public void setProductErrors(List<ProductValidationError> productErrors) {
        this.productErrors = productErrors;
    }
    
    public List<ProductValidationWarning> getProductWarnings() {
        return productWarnings;
    }
    
    public void setProductWarnings(List<ProductValidationWarning> productWarnings) {
        this.productWarnings = productWarnings;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
    
    public long getEndTime() {
        return endTime;
    }
    
    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }
    
    /**
     * 是否验证通过
     * 
     * @return true-通过，false-不通过
     */
    public boolean isValid() {
        return globalErrors.isEmpty() && invalidCount == 0;
    }
    
    /**
     * 是否有警告
     * 
     * @return true-有警告，false-无警告
     */
    public boolean hasWarnings() {
        return !productWarnings.isEmpty();
    }
    
    /**
     * 是否有全局错误
     * 
     * @return true-有全局错误，false-无全局错误
     */
    public boolean hasGlobalErrors() {
        return !globalErrors.isEmpty();
    }
    
    /**
     * 是否有商品错误
     * 
     * @return true-有商品错误，false-无商品错误
     */
    public boolean hasProductErrors() {
        return !productErrors.isEmpty();
    }
    
    @Override
    public String toString() {
        return "BatchValidationResult{" +
                "totalCount=" + totalCount +
                ", validCount=" + validCount +
                ", invalidCount=" + invalidCount +
                ", validRate=" + String.format("%.2f", getValidRate()) + "%" +
                ", duration=" + getDuration() + "ms" +
                ", hasGlobalErrors=" + hasGlobalErrors() +
                ", hasProductErrors=" + hasProductErrors() +
                ", hasWarnings=" + hasWarnings() +
                '}';
    }
}
