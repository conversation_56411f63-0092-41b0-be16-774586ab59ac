package com.ylkj.system.model.vo.omgProducts;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品导入结果VO
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class ImportResultVo {
    
    /** 成功导入的商品数量 */
    private int successCount;
    
    /** 跳过的商品数量（重复SKU） */
    private int skippedCount;
    
    /** 失败的商品数量 */
    private int failedCount;
    
    /** 总处理商品数量 */
    private int totalCount;
    
    /** 跳过的商品SKU列表 */
    private List<String> skippedSkus;
    
    /** 失败的商品SKU列表 */
    private List<String> failedSkus;
    
    /** 导入详细信息 */
    private String message;
    
    public ImportResultVo() {
        this.skippedSkus = new ArrayList<>();
        this.failedSkus = new ArrayList<>();
    }
    
    public ImportResultVo(int totalCount) {
        this();
        this.totalCount = totalCount;
    }
    
    /**
     * 增加成功计数
     */
    public void incrementSuccess() {
        this.successCount++;
    }
    
    /**
     * 增加跳过计数
     */
    public void incrementSkipped() {
        this.skippedCount++;
    }
    
    /**
     * 增加失败计数
     */
    public void incrementFailed() {
        this.failedCount++;
    }
    
    /**
     * 添加跳过的SKU
     */
    public void addSkippedSku(String sku) {
        if (sku != null && !sku.trim().isEmpty()) {
            this.skippedSkus.add(sku);
        }
    }
    
    /**
     * 添加失败的SKU
     */
    public void addFailedSku(String sku) {
        if (sku != null && !sku.trim().isEmpty()) {
            this.failedSkus.add(sku);
        }
    }
    
    /**
     * 生成导入结果消息
     */
    public void generateMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("商品导入完成！");
        sb.append("总计处理: ").append(totalCount).append(" 个商品，");
        sb.append("成功导入: ").append(successCount).append(" 个，");
        sb.append("跳过重复: ").append(skippedCount).append(" 个，");
        sb.append("导入失败: ").append(failedCount).append(" 个");
        
        if (skippedCount > 0) {
            sb.append("。跳过的商品SKU: ").append(String.join(", ", skippedSkus));
        }
        
        if (failedCount > 0) {
            sb.append("。失败的商品SKU: ").append(String.join(", ", failedSkus));
        }
        
        this.message = sb.toString();
    }
    
    /**
     * 判断是否导入成功
     */
    public boolean isSuccess() {
        return successCount > 0 || (totalCount > 0 && failedCount == 0);
    }
    
    // Getter and Setter methods
    public int getSuccessCount() {
        return successCount;
    }
    
    public void setSuccessCount(int successCount) {
        this.successCount = successCount;
    }
    
    public int getSkippedCount() {
        return skippedCount;
    }
    
    public void setSkippedCount(int skippedCount) {
        this.skippedCount = skippedCount;
    }
    
    public int getFailedCount() {
        return failedCount;
    }
    
    public void setFailedCount(int failedCount) {
        this.failedCount = failedCount;
    }
    
    public int getTotalCount() {
        return totalCount;
    }
    
    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }
    
    public List<String> getSkippedSkus() {
        return skippedSkus;
    }
    
    public void setSkippedSkus(List<String> skippedSkus) {
        this.skippedSkus = skippedSkus;
    }
    
    public List<String> getFailedSkus() {
        return failedSkus;
    }
    
    public void setFailedSkus(List<String> failedSkus) {
        this.failedSkus = failedSkus;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    @Override
    public String toString() {
        return "ImportResultVo{" +
                "successCount=" + successCount +
                ", skippedCount=" + skippedCount +
                ", failedCount=" + failedCount +
                ", totalCount=" + totalCount +
                ", message='" + message + '\'' +
                '}';
    }
}
