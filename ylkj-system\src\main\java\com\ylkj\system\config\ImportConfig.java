package com.ylkj.system.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 商品导入配置类
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Component
@ConfigurationProperties(prefix = "omg.import")
public class ImportConfig {
    
    /** 默认批处理大小 */
    private int defaultBatchSize = 500;
    
    /** 最大批处理大小 */
    private int maxBatchSize = 2000;
    
    /** 大数据量阈值 */
    private int largeDataThreshold = 1000;
    
    /** 是否启用性能监控 */
    private boolean enablePerformanceMonitor = true;
    
    /** 是否启用详细日志 */
    private boolean enableDetailLog = true;
    
    /** 数据库连接池最大连接数建议 */
    private int recommendedMaxPoolSize = 20;
    
    /** 批处理超时时间（秒） */
    private int batchTimeoutSeconds = 300;
    
    // Getter and Setter methods
    public int getDefaultBatchSize() {
        return defaultBatchSize;
    }
    
    public void setDefaultBatchSize(int defaultBatchSize) {
        this.defaultBatchSize = defaultBatchSize;
    }
    
    public int getMaxBatchSize() {
        return maxBatchSize;
    }
    
    public void setMaxBatchSize(int maxBatchSize) {
        this.maxBatchSize = maxBatchSize;
    }
    
    public int getLargeDataThreshold() {
        return largeDataThreshold;
    }
    
    public void setLargeDataThreshold(int largeDataThreshold) {
        this.largeDataThreshold = largeDataThreshold;
    }
    
    public boolean isEnablePerformanceMonitor() {
        return enablePerformanceMonitor;
    }
    
    public void setEnablePerformanceMonitor(boolean enablePerformanceMonitor) {
        this.enablePerformanceMonitor = enablePerformanceMonitor;
    }
    
    public boolean isEnableDetailLog() {
        return enableDetailLog;
    }
    
    public void setEnableDetailLog(boolean enableDetailLog) {
        this.enableDetailLog = enableDetailLog;
    }
    
    public int getRecommendedMaxPoolSize() {
        return recommendedMaxPoolSize;
    }
    
    public void setRecommendedMaxPoolSize(int recommendedMaxPoolSize) {
        this.recommendedMaxPoolSize = recommendedMaxPoolSize;
    }
    
    public int getBatchTimeoutSeconds() {
        return batchTimeoutSeconds;
    }
    
    public void setBatchTimeoutSeconds(int batchTimeoutSeconds) {
        this.batchTimeoutSeconds = batchTimeoutSeconds;
    }
    
    @Override
    public String toString() {
        return "ImportConfig{" +
                "defaultBatchSize=" + defaultBatchSize +
                ", maxBatchSize=" + maxBatchSize +
                ", largeDataThreshold=" + largeDataThreshold +
                ", enablePerformanceMonitor=" + enablePerformanceMonitor +
                ", enableDetailLog=" + enableDetailLog +
                ", recommendedMaxPoolSize=" + recommendedMaxPoolSize +
                ", batchTimeoutSeconds=" + batchTimeoutSeconds +
                '}';
    }
}
