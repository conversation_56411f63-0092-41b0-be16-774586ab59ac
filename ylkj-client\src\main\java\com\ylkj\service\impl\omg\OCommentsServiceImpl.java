package com.ylkj.service.impl.omg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylkj.mapper.omg.OCommentsMapper;
import com.ylkj.model.domain.omg.OComments;
import com.ylkj.model.vo.LikeCheckRequest;
import com.ylkj.service.omg.OICommentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0.0
 * @Project：agtfind-backbend
 * @Package com.ylkj.service.impl.omg
 * @Title: OCommentsServiceImpl
 * @author: 小许
 * @date: 2025/5/19/周一 10:50
 * @description: omg_商品评论表服务实现类
 */
@Service
public class OCommentsServiceImpl extends ServiceImpl<OCommentsMapper, OComments> implements OICommentsService{

    private static final int SUCCESS_CODE = 0;
    private static final int ERROR_CODE = -1;

    @Autowired
    private OCommentsMapper commentsMapper;

    @Override
    public Integer addProductComment(Long productId, OComments commentDTO) {
        try {

            Integer i = commentsMapper.addProductComment(productId, commentDTO.getUserId(), commentDTO.getContent(), commentDTO.getImages());
            if (i != SUCCESS_CODE) {
                return i;
            }
        } catch (ArithmeticException e) {

            return ERROR_CODE;
        }
        return null;
    }

    /**
     * @author: 小许
     * @date: 2025/5/19/周一 16:08
     * @description: 获取用户是否点赞
     * @param vo
     * @return List<OComments>
     */
    @Override
    public List<OComments> checkLikeStatus(LikeCheckRequest vo) {
       return commentsMapper.checkLikeStatus(vo);
    }

    /**
    * @Author: 吴居乐
    * @Description:
    * @DateTime: 16:41 2025/7/8/周二
    * @Params: [commentId, userId]
    * @Return int
    */
    @Override
    @Transactional
    public int likeComment(Long commentId, Long userId) {
        try {
            // 提取公共操作
            boolean success = false;
            if (commentsMapper.selectBycommentIdAndUserId(commentId, userId)) {
                // 已点赞，更新状态
                commentsMapper.likeCommentStatus(commentId, userId);
                success = true;
            } else {
                // 未点赞，创建记录
                commentsMapper.createLikeComment(commentId, userId);
                success = true;
            }

            // 两种情况都需要更新点赞数，放在外面
            if (success) {
                commentsMapper.likeComment(commentId);
            }

            return 1;
        } catch (Exception e) {

            return 0;
        }
    }

    @Override
    public int unlikeComment(Long commentId, Long userId) {
        commentsMapper.unlikeComment(commentId);
        return commentsMapper.unlikeCommentStatus(commentId,userId);
    }

    /**
     * 回复商品评论
     * @param requestData 请求数据
     * @return 回复结果
     */
    @Override
    @Transactional
    public int replyComment(Map<String, Object> requestData) {
        try {
            // 创建OComments对象
            OComments replyComment = new OComments();

            // 设置基本字段
            replyComment.setProductId(Long.valueOf(requestData.get("productId").toString()));
            replyComment.setUserId(Long.valueOf(requestData.get("userId").toString()));
            replyComment.setContent((String) requestData.get("content"));
            replyComment.setParentCommentId(Long.valueOf(requestData.get("parentCommentId").toString()));

            // 处理图片字段（可选）
            if (requestData.get("images") != null) {
                replyComment.setImages((String) requestData.get("images"));
            }

            // 设置回复时间
            replyComment.setCreatedAt(LocalDateTime.now());
            replyComment.setUpdatedAt(LocalDateTime.now());

            // 插入回复评论
            return commentsMapper.insert(replyComment);

        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
}
