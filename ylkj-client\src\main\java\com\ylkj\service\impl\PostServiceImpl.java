package com.ylkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylkj.mapper.PostMapper;
import com.ylkj.model.domain.PostTags;
import com.ylkj.model.domain.Posts;
import com.ylkj.model.domain.Tags;
import com.ylkj.model.domain.Users;
import com.ylkj.model.domain.omg.PointTransactions;
import com.ylkj.model.domain.omg.PostComments;
import com.ylkj.model.domain.omg.PostImages;
import com.ylkj.model.vo.PostDetailVO;
import com.ylkj.model.vo.PostVO;
import com.ylkj.model.vo.UserVo;
import com.ylkj.service.IPostService;
import com.ylkj.service.IPostTagService;
import com.ylkj.service.ITagService;
import com.ylkj.service.IUsersService;
import com.ylkj.service.omg.IPointTransactionsService;
import com.ylkj.service.omg.IPostCommentsService;
import com.ylkj.service.omg.IPostImagesService;
import com.ylkj.service.omg.IUserPointService;
import com.ylkj.utils.CommentTreeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @BelongsProject: AGTFIND-backbend
 * @BelongsPackage: com.ylkj.service.impl
 * @Author: 小林
 * @CreateTime: 2025-05-08 15:34
 * @Description: 帖子表service层
 * @Version: 1.0
 */
@Service
public class PostServiceImpl extends ServiceImpl<PostMapper, Posts> implements IPostService {

    private static final Logger logger = LoggerFactory.getLogger(PostServiceImpl.class);

    @Autowired
    private ITagService tagService;

    @Autowired
    private IPostTagService postTagService;

    @Autowired
    private IUsersService usersService;

    @Autowired
    private IPostImagesService postImagesService;

    @Autowired
    private IPostCommentsService postCommentsService;
    @Autowired
    private IUserPointService userPointService;
    @Autowired
    private IPointTransactionsService pointTransactionsService;


    /**
     * 根据标签 ID 获取帖子列表
     *
     * @param tagId 标签 ID
     * @return 帖子列表
     */
    @Override
    public List<Posts> getPostsByTagId(Long tagId) {
        List<Posts> posts = baseMapper.getPostsByTagId(tagId);
        posts.forEach(post -> {
            post.setUsers(usersService.getById(post.getUserId()));
        });
        return posts;
    }

    /**
     * 获取所有帖子
     *
     * @param title 标题关键词
     * @param orderBy 排序方式 ("time" 或 "views")
     * @return 帖子列表
     */
    @Override
    public List<Posts> getAllPosts(String title, String orderBy) {

        // 获取所有帖子
        List<Posts> posts = baseMapper.selectList(title, orderBy);

        // 如果是按浏览量排序，再次确保按浏览量降序排序
        if ("views".equals(orderBy)) {
            // 在Java中再次排序，确保浏览量为null的情况也能正确处理
            posts.sort((a, b) -> {
                Integer viewsA = a.getViews() == null ? 0 : a.getViews();
                Integer viewsB = b.getViews() == null ? 0 : b.getViews();
                return viewsB.compareTo(viewsA); // 降序排列
            });
            System.out.println("已按浏览量降序重新排序");
        }

        // 为每个帖子设置用户信息(使用UserVo)
        posts.forEach(post -> {
            // 获取完整用户信息
            Users user = usersService.getById(post.getUserId());

            if (user != null) {
                // 创建UserVo并设置需要的字段
                UserVo userVo = new UserVo();
                userVo.setId(Math.toIntExact(user.getUserId()));
                userVo.setUsername(user.getUsername());
                userVo.setAvatarUrl(user.getAvatarUrl());
                userVo.setEmail(user.getEmail());
                userVo.setPhoneNumber(user.getPhoneNumber());

                // 设置到帖子对象
                post.setUserVo(userVo);
            }
            
            // 无论是否有用户信息，都要设置图片和评论数量
            List<PostImages> postImages = postImagesService.getPostImages(post.getId());
            post.setImages(postImages);
            
            // 最后设置评论数量，避免被覆盖
            int count = postCommentsService.selectCountByPostId(post.getId());
            post.setCount(count);
        });

        return posts;
    }

    /**
     * 根据 ID 获取帖子
     *
     * @param id 帖子 ID
     * @return 帖子对象
     */
    @Override
    public Posts getPostById(Long id) {
        return baseMapper.getById(id);
    }

    /**
     * 创建帖子
     * @param post 帖子对象
     * @return
     */
    @Override
    public boolean createPost(Posts post) {
        // 判断帖子标题是否为空
        if (post.getTitle() == null || post.getTitle().isEmpty()) {
            System.err.println("帖子标题为空");
            return false;
        }

        // 判断帖子标题是否重复
        QueryWrapper<Posts> postQueryWrapper = new QueryWrapper<>();
        postQueryWrapper.eq("title", post.getTitle());
        Long count = baseMapper.selectCount(postQueryWrapper);

        if (count > 0) {
            System.err.println("帖子标题已存在");
            return false;
        }

        try {
            // 插入帖子基本信息到帖子表
            boolean postResult = baseMapper.insert(post) > 0;
            if (!postResult) {
                System.err.println("插入帖子失败");
                return false;
            }

            // 判断是否有标签需要新增
            List<String> tagNames = post.getTags();
            if (tagNames != null && !tagNames.isEmpty()) {
                List<Long> tagIds = new ArrayList<>(); // 用于存储标签ID

                for (String tagName : tagNames) {
                    System.out.println("处理的标签名：" + tagName); // 打印当前处理的标签名

                    // 判断标签名是否为空
                    if (tagName == null || tagName.isEmpty()) {
                        System.err.println("标签名为空：" + tagName);
                        return false;
                    }

                    // 方法一：获取所有标签后手动过滤
                    List<Tags> allTags = tagService.getBaseMapper().selectList(null);
                    Tags existingTag = null;
                    for (Tags tag : allTags) {
                        if (tag.getName().equals(tagName)) {
                            existingTag = tag;
                            break;
                        }
                    }

                    Long tagId;
                    if (existingTag != null) {
                        // 如果标签存在，获取标签ID
                        tagId = existingTag.getId();
                        System.out.println("使用现有标签ID：" + tagId + " 标签名：" + existingTag.getName());
                    } else {
                        // 如果标签不存在，插入标签到标签表
                        Tags newTag = new Tags();
                        newTag.setName(tagName);
                        int result = tagService.createTag(newTag);

                        if (result <= 0) {
                            System.err.println("插入标签失败: " + tagName);
                            return false;
                        }
                        tagId = newTag.getId(); // 获取新插入的标签ID
                        System.out.println("新插入标签ID：" + tagId + " 标签名：" + tagName);
                    }

                    // 添加到tagIds集合中
                    tagIds.add(tagId);
                }

                // 插入关联关系到关联表
                for (Long tagId : tagIds) {
                    PostTags postTag = new PostTags();
                    postTag.setPostId(post.getId());
                    postTag.setTagId(tagId);
                    boolean postTagResult = postTagService.createPostTag(postTag);
                    if (!postTagResult) {
                        System.err.println("插入关联关系失败：post_id=" + post.getId() + " tag_id=" + tagId);
                        return false;
                    }
                }

                // 处理图片关联
                String[] imageUrls = post.getImageUrls();
                postImagesService.uploadPostImage(post.getId(), imageUrls);
                //判断每日获得的积分小于10才能执行增加积分和创建积分记录操作。
//                Map<String, Object> dailyPointsInfo = userPointService.getDailyPointsInfo(Math.toIntExact(post.getUserId()));
//                Integer todayPoints =(Integer) dailyPointsInfo.get("todayPoints");

//                if(todayPoints!=null && todayPoints<10){
//                    //用户发布帖子后可以增加积分
//                    userPointService.addPoints(Math.toIntExact(post.getUserId()), 3, "发布帖子");
//                    PointTransactions pointTransactions = new PointTransactions();
//                    pointTransactions.setUserId(Math.toIntExact(post.getUserId()));
//                    pointTransactions.setPoints(3);
//                    pointTransactions.setType("Publish post");
//                    pointTransactionsService.createPointTransactions(pointTransactions);
//                }else {
//                    System.err.println("今日已获得十积分，无法增加积分");
//                }

            }
            return postResult;
        } catch (Exception e) {
            System.err.println("创建帖子时发生异常：" + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 更新帖子
     *
     * @param post 帖子对象
     * @return 是否成功
     */
    @Override
    public boolean updatePost(Posts post) {
        try {
            // 1. 更新帖子基本信息
            boolean updateResult = baseMapper.updateById(post) > 0;
            if (!updateResult) {
                return false;
            }

            // 2. 处理标签关系 - 如果有传入标签
            List<String> tagNames = post.getTags();
            if (tagNames != null && !tagNames.isEmpty()) {
                // 2.1 先删除原有的帖子-标签关联
                QueryWrapper<PostTags> deleteWrapper = new QueryWrapper<>();
                deleteWrapper.eq("post_id", post.getId());
                postTagService.remove(deleteWrapper);

                // 2.2 添加新的标签关联
                List<Long> tagIds = new ArrayList<>();
                for (String tagName : tagNames) {
                    if (tagName == null || tagName.isEmpty()) {
                        continue; // 跳过空标签名
                    }

                    // 方法一：手动查找标签
                    List<Tags> allTags = tagService.getBaseMapper().selectList(null);
                    Tags existingTag = null;
                    for (Tags tag : allTags) {
                        if (tag.getName().equals(tagName)) {
                            existingTag = tag;
                            break;
                        }
                    }

                    Long tagId;
                    if (existingTag != null) {
                        // 标签已存在
                        tagId = existingTag.getId();
                        System.out.println("使用现有标签ID：" + tagId + " 标签名：" + tagName);
                    } else {
                        // 创建新标签
                        Tags newTag = new Tags();
                        newTag.setName(tagName);
                        int result = tagService.createTag(newTag);
                        if (result <= 0) {
                            System.err.println("创建标签失败: " + tagName);
                            continue;
                        }
                        tagId = newTag.getId();
                        System.out.println("新创建标签ID：" + tagId + " 标签名：" + tagName);
                    }

                    // 防止重复ID
                    if (!tagIds.contains(tagId)) {
                        tagIds.add(tagId);
                    }
                }

                // 创建新的关联
                for (Long tagId : tagIds) {
                    PostTags postTag = new PostTags();
                    postTag.setPostId(post.getId());
                    postTag.setTagId(tagId);
                    postTagService.createPostTag(postTag);
                    System.out.println("创建关联：帖子ID=" + post.getId() + " 标签ID=" + tagId);
                }

                //删除图片
                postImagesService.deletePostImage(post.getId());


                // 处理图片关联
                String[] imageUrls = post.getImageUrls();
                postImagesService.uploadPostImage(post.getId(), imageUrls);



            }

            return true;
        } catch (Exception e) {
            System.err.println("更新帖子时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除帖子
     *
     * @param id 帖子 ID
     * @return 是否成功
     */
    @Override
    public boolean deletePost(Long id) {
        try {
            // 1. 先根据帖子ID查询帖子信息
            Posts post = baseMapper.getById(id);
            if (post == null) {
                System.err.println("帖子不存在，ID: " + id);
                return false;
            }

            Long userId = post.getUserId(); // 保存用户ID用于后续积分操作

            // 2. 删除帖子标签关联表中的记录
            QueryWrapper<PostTags> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("post_id", id);
            boolean deleteTagsResult = postTagService.remove(queryWrapper);

            // 3. 再删除帖子表中的记录
            boolean deletePostResult = baseMapper.deleteById(id) > 0;

            // 4. 如果删除成功，则扣除积分
//            if (deletePostResult) {
//                userPointService.addPoints(Math.toIntExact(userId), -3, "删除帖子");
//
//                PointTransactions pointTransactions = new PointTransactions();
//                pointTransactions.setUserId(Math.toIntExact(userId));
//                pointTransactions.setPoints(-3);
//                pointTransactions.setType("删除帖子");
//                pointTransactions.setDescription("删除帖子");
//                pointTransactions.setCreatedAt(LocalDateTime.now());
//                pointTransactionsService.createPointTransactions(pointTransactions);
//            }

            return deletePostResult;
        } catch (Exception e) {
            System.err.println("删除帖子时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    @Override
    public boolean incrementPostViews(Long postId) {
        if (postId == null) {
            return false;
        }
        try {
            return baseMapper.incrementPostViews(postId) > 0;
        } catch (Exception e) {
            System.err.println("增加浏览量失败: " + e.getMessage());
            return false;
        }
    }

    @Override
    public int getPostViews(Long postId) {
        if (postId == null) {
            return 0;
        }
        try {
            Integer views = baseMapper.getPostViews(postId);
            return views == null ? 0 : views; // 安全处理null值
        } catch (Exception e) {
            System.err.println("获取浏览量失败: " + e.getMessage());
            return 0; // 出错时返回0而不是抛出异常
        }
    }
    /**
     * 根据用户ID获取用户发布的帖子
     *
     * @param userId 用户ID
     * @return 用户发布的帖子列表
     */
    @Override
    public List<PostVO> getPostsByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        return baseMapper.getPostsByUserId(userId);
    }

    @Override
    public PostDetailVO getPostDetailById(Long postId) {
        if (postId == null) {
            logger.warn("获取帖子详情失败 - 帖子ID为空");
            return null;
        }

        logger.info("开始获取帖子详情 - 帖子ID: {}", postId);

        try {
            // 创建返回对象
            PostDetailVO detailVO = new PostDetailVO();

            // 1. 获取帖子基本信息
            Posts post = baseMapper.getPostDetailById(postId);
            if (post == null) {
                logger.warn("获取帖子详情失败 - 帖子不存在, ID: {}", postId);
                return null;
            }

            logger.debug("获取到帖子基本信息 - 标题: {}", post.getTitle());

            // 设置帖子基本信息
            detailVO.setId(post.getId());
            detailVO.setTitle(post.getTitle());
            detailVO.setContent(post.getContent());
            detailVO.setLikes(post.getLikes());
            detailVO.setFavorites(post.getFavorites());
            detailVO.setViews(post.getViews());
            detailVO.setCreateTime(post.getCreateTime());
            detailVO.setUpdateTime(post.getUpdateTime());
            detailVO.setUserId(post.getUserId());

            // 2. 获取用户信息
            Users user = usersService.getById(post.getUserId());
            if (user != null) {
                logger.debug("获取到用户信息 - 用户名: {}", user.getUsername());
                detailVO.setUsername(user.getUsername());
                detailVO.setAvatarUrl(user.getAvatarUrl());
                detailVO.setEmail(user.getEmail());
                detailVO.setPhoneNumber(user.getPhoneNumber());
            } else {
                logger.warn("获取用户信息失败 - 用户不存在, ID: {}", post.getUserId());
            }

            // 3. 获取标签信息
            List<Tags> tags = tagService.getTagsByPostId(post.getId());
            detailVO.setTags(tags);

            // 提取标签名称列表
            if (tags != null && !tags.isEmpty()) {
                List<String> tagNames = tags.stream()
                        .map(Tags::getName)
                        .collect(Collectors.toList());
                detailVO.setTagNames(tagNames);
                logger.debug("获取到标签信息 - 数量: {}, 标签: {}", tags.size(), tagNames);
            } else {
                logger.debug("帖子没有标签 - 帖子ID: {}", postId);
            }

            // 4. 获取图片信息
            List<PostImages> images = postImagesService.getPostImages(post.getId());
            detailVO.setImages(images);

            // 提取图片URL列表
            if (images != null && !images.isEmpty()) {
                List<String> imageUrls = images.stream()
                        .map(PostImages::getImageUrl)
                        .collect(Collectors.toList());
                detailVO.setImageUrls(imageUrls);
                logger.debug("获取到图片信息 - 数量: {}", images.size());
            } else {
                logger.debug("帖子没有图片 - 帖子ID: {}", postId);
            }

            // 5. 获取评论信息
            List<PostComments> comments = postCommentsService.getCommentsByPostId(post.getId());

            // 使用CommentTreeUtil将评论转换为树形结构
            List<PostComments> commentTree = CommentTreeUtil.buildTree(comments);

            detailVO.setComments(commentTree); // 设置树形结构的评论
            detailVO.setCommentCount(comments.size());
            logger.debug("获取到评论信息 - 数量: {}, 树形结构转换完成", comments.size());

            // 6. 增加浏览量
//            boolean incrementResult = this.incrementPostViews(postId);
//            if (incrementResult) {
//                logger.debug("增加浏览量成功 - 帖子ID: {}", postId);
//            } else {
//                logger.warn("增加浏览量失败 - 帖子ID: {}", postId);
//            }

            logger.info("获取帖子详情成功 - 帖子ID: {}", postId);
            return detailVO;
        } catch (Exception e) {
            logger.error("获取帖子详情异常 - 帖子ID: {}", postId, e);
            return null;
        }
    }
}
