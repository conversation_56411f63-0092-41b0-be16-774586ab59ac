package com.ylkj.system.model.domain;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Date;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.ylkj.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ylkj.common.enums.ProductAttributesEnum;

/**
 * omg_商品对象 omg_products
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@TableName("omg_products")
@Data
public class OmgProducts implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商品唯一标识
     */
    @TableId(value = "product_id", type = IdType.ASSIGN_ID)
    private Long productId;

    /**
     * 关联卖家ID
     */
    private Long sellerId;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 商品名称
     */
    @Excel(name = "商品名称")
    private String name;

    /**
     * 商品SKU编号
     */
    @Excel(name = "商品SKU")
    private String sku;

    /**
     * 商品价格
     */
    @Excel(name = "商品价格")
    private BigDecimal price;

    /**
     * 商品原价（用于折扣计算）
     */
    @Excel(name = "商品原价")
    private BigDecimal originalPrice;

    /**
     * 商品点赞数
     */
    @Excel(name = "商品点赞数")
    private Long likes;

    /**
     * 商品浏览量
     */
    @Excel(name = "商品浏览量")
    private Long views;

    /**
     * 虚拟浏览量
     */
    @Excel(name = "虚拟浏览量")
    private Long virtualViews;

    /**
     * 商品所属商家
     */
    @Excel(name = "所属商家")
    private String merchant;

    /**
     * 商品所属平台
     */
    @Excel(name = "所属平台")
    private String platform;

    /**
     * 商品状态
     */
    @Excel(name = "商品状态")
    private String status;

    /**
     * 商品分类
     */
    @Excel(name = "商品分类")
    @TableField(exist = false)
    private String categoryName;

    /**
     * 所属二级分类
     * 注意：当前分类表不支持层级分类，暂时注释掉Excel导出
     */
    // @Excel(name = "所属二级分类")
    @TableField(exist = false)
    private String secondCategoryName;

    /**
     * 微店名称
     */
    @Excel(name = "微店名称")
    @TableField(exist = false)
    private String storeName;

    /**
     * 商品标签
     */
    @TableField(exist = false)
    private String tag;

    /**
     * 原链接
     */
    @TableField(exist = false)
    private String fromUrl;

    /**
     * 商品推荐状态
     */
    @TableField(exist = false)
    private Integer productStatus;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品标识符（用于URL）
     */
    private String slug;

    /**
     * 商品主图链接
     */
    private String mainImage;

    /**
     * 商品库存
     */
    private Long stock;

    /**
     * 商品评分
     */
    private BigDecimal rating;

    /**
     * 评分总数
     */
    private Long totalRatings;

    /**
     * 商品创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdAt;

    /**
     * 商品最后更新时间
     */
    private Date updatedAt;

    /**
     * 关联品牌ID
     */
    @TableField(exist = false)
    private Long brandId;

    /**
     * 关联品牌信息
     */
    @TableField(exist = false)
    private OmgBrands OmgBrand;

    /**
     * 商品图片信息
     */
    @TableField(exist = false)
    private List<OmgProductImages> qcImages;

    /**
     * QC数量
     */
    @TableField(exist = false)
    private String qc;

    private String video;

    /**
     * 运输天数
     */
    private String averageArrival;

    /**
     * 商品重量
     */
    private BigDecimal weight;

    /**
     * 商品属性：SP是标品，REP是仿品
     */
    @Excel(name = "商品属性")
    private String productAttributes;

    /** 最低价格 */
    private BigDecimal minPrice;

    /** 最高价格 */
    private BigDecimal maxPrice;

    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;
}
