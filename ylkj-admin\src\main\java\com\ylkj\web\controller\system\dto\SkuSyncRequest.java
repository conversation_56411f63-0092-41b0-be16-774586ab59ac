package com.ylkj.web.controller.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * SKU同步请求对象
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@ApiModel(value = "SKU同步请求对象", description = "用于CNFans数据同步的SKU请求参数")
public class SkuSyncRequest {

    @ApiModelProperty(value = "单个SKU", example = "SKU001")
    private String sku;

    @ApiModelProperty(value = "SKU列表", example = "[\"SKU001\", \"SKU002\", \"SKU003\"]")
    private List<String> skuList;

    @ApiModelProperty(value = "SKU字符串（多个SKU用逗号、分号或空格分隔）", example = "SKU001,SKU002,SKU003")
    private String skuString;

    @ApiModelProperty(value = "批处理大小（默认50）", example = "50")
    private Integer batchSize = 50;

    @ApiModelProperty(value = "是否强制同步（忽略缓存）", example = "false")
    private boolean forceSync = false;

    @ApiModelProperty(value = "同步超时时间（秒，默认300）", example = "300")
    private Integer timeoutSeconds = 300;

    @ApiModelProperty(value = "是否异步执行", example = "false")
    private boolean async = false;

    @ApiModelProperty(value = "备注信息", example = "手动触发的同步任务")
    private String remark;
}
