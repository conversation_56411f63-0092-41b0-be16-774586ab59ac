package com.ylkj.service;

import com.ylkj.model.domain.SyncResult;

import java.util.List;

/**
 * CNFans数据同步服务接口
 * 用于将CNFans的商品信息同步到OMG商品表
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface ICnfansDataSyncService {

    /**
     * 根据SKU同步单个商品的CNFans数据
     * 
     * @param sku 商品SKU
     * @return 是否同步成功
     */
    boolean syncProductBySku(String sku);

    /**
     * 根据SKU字符串同步商品数据（支持单个或多个SKU，逗号分隔）
     * 
     * @param skuString SKU字符串，多个SKU用逗号、分号或空格分隔
     * @return 同步结果统计
     */
    SyncResult syncProductsBySkuString(String skuString);

    /**
     * 批量同步商品的CNFans数据
     * 
     * @param skuList SKU列表
     * @return 同步结果统计
     */
    SyncResult batchSyncProducts(List<String> skuList);

    /**
     * 同步所有商品的CNFans数据
     * 
     * @return 同步结果统计
     */
    SyncResult syncAllProducts();
}
