package com.ylkj.service;

import com.ylkj.model.domain.omg.FindQcImageProcessResult;
import com.ylkj.service.FindQcApiService.FindQcProductInfo;
import com.ylkj.system.service.IOmgProductMainImagesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * FindQC图片处理服务
 * 整合FindQC API调用和图片下载上传功能
 */
@Slf4j
@Service
public class FindQcImageProcessService {

    @Autowired
    private FindQcApiService findQcApiService;

    @Autowired
    private ImageDownloadUploadService imageDownloadUploadService;

    @Autowired
    private IOmgProductMainImagesService omgProductMainImagesService;

    /**
     * 获取商品信息并处理图片
     * 
     * @param itemId 商品ID
     * @param mallType 商城类型
     * @param currencyType 货币类型 (默认: USD)
     * @param langType 语言类型 (默认: en)
     * @return 处理结果
     */
    public FindQcImageProcessResult processProductImages(String itemId, String mallType, 
                                                        String currencyType, String langType) {
        
        // 设置默认值
        if (currencyType == null || currencyType.isEmpty()) {
            currencyType = "USD";
        }
        if (langType == null || langType.isEmpty()) {
            langType = "en";
        }
        
        log.info("开始处理FindQC商品图片，itemId: {}, mallType: {}", itemId, mallType);
        
        try {
            // 1. 调用FindQC API获取商品信息
            FindQcProductInfo productInfo = findQcApiService.getProductDetail(itemId, mallType, currencyType, langType);
            
            if (productInfo == null) {
                log.error("获取FindQC商品信息失败，itemId: {}", itemId);
                return createErrorResult("获取商品信息失败");
            }
            
            // 2. 处理picList图片
            List<String> picListOssUrls = new ArrayList<>();
            if (productInfo.getPicList() != null && !productInfo.getPicList().isEmpty()) {
                log.info("开始处理picList图片，数量: {}", productInfo.getPicList().size());
                picListOssUrls = imageDownloadUploadService.downloadAndUploadImages(
                        productInfo.getPicList(), itemId + "_pic");

                // 保存picList图片到主图表
                if (!picListOssUrls.isEmpty()) {
                    omgProductMainImagesService.saveFindQcImages(
                            itemId, // SKU就是itemId的值
                            itemId,
                            mallType,
                            productInfo.getPicList(),
                            picListOssUrls,
                            "findqc_pic"
                    );
                }
            }

            // 3. qcList图片不需要处理，直接跳过
            List<String> qcListOssUrls = new ArrayList<>();
            log.info("qcList图片跳过处理，数量: {}",
                    productInfo.getQcList() != null ? productInfo.getQcList().size() : 0);
            
            // 4. 创建处理结果
            FindQcImageProcessResult result = new FindQcImageProcessResult();
            result.setSuccess(true);
            result.setMessage("图片处理完成");
            result.setProductInfo(productInfo);
            result.setPicListOssUrls(picListOssUrls);
            result.setQcListOssUrls(qcListOssUrls);
            result.setTotalOriginalImages(
                    productInfo.getPicList() != null ? productInfo.getPicList().size() : 0
            );
            result.setTotalUploadedImages(picListOssUrls.size());
            
            log.info("FindQC图片处理完成，itemId: {}, 原始picList图片: {}, 成功上传: {}",
                    itemId, result.getTotalOriginalImages(), result.getTotalUploadedImages());
            
            return result;
            
        } catch (Exception e) {
            log.error("处理FindQC商品图片异常，itemId: {}", itemId, e);
            return createErrorResult("处理图片异常: " + e.getMessage());
        }
    }

    /**
     * 只处理picList图片
     * 
     * @param itemId 商品ID
     * @param mallType 商城类型
     * @param currencyType 货币类型
     * @param langType 语言类型
     * @return 处理结果
     */
    public FindQcImageProcessResult processPicListOnly(String itemId, String mallType, 
                                                      String currencyType, String langType) {
        
        log.info("开始处理FindQC商品picList图片，itemId: {}, mallType: {}", itemId, mallType);
        
        try {
            // 1. 调用FindQC API获取商品信息
            FindQcProductInfo productInfo = findQcApiService.getProductDetail(itemId, mallType, currencyType, langType);
            
            if (productInfo == null) {
                return createErrorResult("获取商品信息失败");
            }
            
            // 2. 只处理picList图片
            List<String> picListOssUrls = new ArrayList<>();
            if (productInfo.getPicList() != null && !productInfo.getPicList().isEmpty()) {
                picListOssUrls = imageDownloadUploadService.downloadAndUploadImages(
                        productInfo.getPicList(), itemId + "_pic");

                // 保存picList图片到主图表
                if (!picListOssUrls.isEmpty()) {
                    omgProductMainImagesService.saveFindQcImages(
                            itemId, // SKU就是itemId的值
                            itemId,
                            mallType,
                            productInfo.getPicList(),
                            picListOssUrls,
                            "findqc_pic"
                    );
                }
            }
            
            // 3. 创建处理结果
            FindQcImageProcessResult result = new FindQcImageProcessResult();
            result.setSuccess(true);
            result.setMessage("picList图片处理完成（qcList图片已跳过）");
            result.setProductInfo(productInfo);
            result.setPicListOssUrls(picListOssUrls);
            result.setQcListOssUrls(new ArrayList<>()); // 空列表
            result.setTotalOriginalImages(productInfo.getPicList() != null ? productInfo.getPicList().size() : 0);
            result.setTotalUploadedImages(picListOssUrls.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("处理FindQC商品picList图片异常，itemId: {}", itemId, e);
            return createErrorResult("处理picList图片异常: " + e.getMessage());
        }
    }

    /**
     * 创建错误结果
     * 
     * @param message 错误消息
     * @return 错误结果
     */
    private FindQcImageProcessResult createErrorResult(String message) {
        FindQcImageProcessResult result = new FindQcImageProcessResult();
        result.setSuccess(false);
        result.setMessage(message);
        result.setPicListOssUrls(new ArrayList<>());
        result.setQcListOssUrls(new ArrayList<>());
        result.setTotalOriginalImages(0);
        result.setTotalUploadedImages(0);
        return result;
    }


}
