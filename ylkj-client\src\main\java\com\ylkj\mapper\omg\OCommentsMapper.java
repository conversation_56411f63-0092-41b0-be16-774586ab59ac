package com.ylkj.mapper.omg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylkj.model.domain.Comments;
import com.ylkj.model.domain.omg.OComments;
import com.ylkj.model.vo.LikeCheckRequest;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.util.List;

/**
 * @version 1.0.0
 * @Project：back
 * @Package com.ylkj.mapper.omg
 * @Title: OCommentsMapper
 * @author: 小许
 * @date: 2025/5/15/周四 19:42
 * @description: omg_商品评价表
 */
public interface OCommentsMapper extends BaseMapper<OComments> {

    List<OComments> getCommentsByProductId(Long productId);

    Integer addProductComment(@Param("productId") Long productId, @Param("userId") Long userId,@Param("content") String content,@Param("images") String images);

    List<OComments> checkLikeStatus(@Param("vo") LikeCheckRequest vo);

    boolean selectBycommentIdAndUserId(@Param("commentId")Long commentId,@Param("userId") Long userId);

    int likeComment(Long commentId);

    int likeCommentStatus(@Param("commentId")Long commentId,@Param("userId") Long userId);

    int createLikeComment(@Param("commentId") Long commentId,@Param("userId") Long userId);

    int unlikeComment(Long commentId);

    int unlikeCommentStatus(@Param("commentId") Long commentId,@Param("userId") Long userId);

    /**
     * 根据父评论ID获取回复列表
     * @param parentCommentId 父评论ID
     * @return 回复列表
     */
    List<OComments> getRepliesByParentCommentId(@Param("parentCommentId") Long parentCommentId);
}
