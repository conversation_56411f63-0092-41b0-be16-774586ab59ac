package com.ylkj.service.omg;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylkj.common.enums.VideoTypeEnum;
import com.ylkj.model.domain.OmgHomeVideoClient;
import com.ylkj.model.vo.OmgHomeVideoClientVO;

import java.util.List;

/**
 * omg首页视频Service接口（客户端）
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IOmgHomeVideoClientService extends IService<OmgHomeVideoClient> {
    
    /**
     * 获取首页视频列表
     *
     * @return 首页视频VO列表
     */
    List<OmgHomeVideoClientVO> getHomeVideoList();

    /**
     * 根据类型获取首页视频列表
     *
     * @param type 视频类型字符串
     * @return 首页视频VO列表
     */
    List<OmgHomeVideoClientVO> getHomeVideoListByType(String type);
} 