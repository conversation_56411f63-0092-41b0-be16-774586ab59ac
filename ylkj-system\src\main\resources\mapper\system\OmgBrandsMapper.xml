<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.system.mapper.OmgBrandsMapper">
    
    <resultMap type="OmgBrands" id="OmgBrandsResult">
        <result property="brandId"    column="brand_id"    />
        <result property="name"    column="name"    />
        <result property="slug"    column="slug"    />
        <result property="logoUrl"    column="logo_url"    />
        <result property="description"    column="description"    />
        <result property="parentCategoryId"    column="parent_category_id"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />

        <association property="sOmgCategories" javaType="com.ylkj.system.model.domain.SOmgCategories">
            <result property="categoryId"    column="category_id"    />
            <result property="name"    column="name"    />
            <result property="createdAt"    column="created_at"    />
        </association>
    </resultMap>

    <resultMap type="OmgBrands" id="OmgBrandsSelect">
        <result property="brandId"    column="brand_id"    />
        <result property="name"    column="name"    />
        <result property="slug"    column="slug"    />
        <result property="logoUrl"    column="logo_url"    />
        <result property="description"    column="description"    />
        <result property="parentCategoryId"    column="parent_category_id"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />

        <association property="sOmgCategories" javaType="com.ylkj.system.model.domain.SOmgCategories">
            <result property="categoryId"    column="category_id"    />
            <result property="name"    column="category_name"    />
            <result property="createdAt"    column="category_created_at"  />
        </association>
    </resultMap>

    <sql id="selectOmgBrandsVo">
        select brand_id, name, slug, logo_url, description, parent_category_id, created_at, updated_at from omg_brands
    </sql>
    <sql id="selectOmgBrandsWithCategoryVo">
        select
            b.brand_id,
            b.name,
            b.slug,
            b.logo_url,
            b.description,
            b.parent_category_id,
            b.created_at,
            b.updated_at,
            c.category_id,
            c.name as category_name,
            c.created_at as category_created_at
        from omg_brands b
                 left join omg_categories c on b.parent_category_id = c.category_id
    </sql>

    <select id="selectOmgBrandsList" parameterType="OmgBrands" resultMap="OmgBrandsSelect">
        <include refid="selectOmgBrandsWithCategoryVo"/>
        <where>
            <if test="name != null  and name != ''"> and b.name like concat('%', #{name}, '%')</if>
            <if test="slug != null  and slug != ''"> and b.slug like concat('%', #{slug}, '%')</if>
            <if test="createdAt != null "> and b.created_at = #{createdAt}</if>
            <if test="categoryName != null and categoryName != ''">
                AND c.name LIKE CONCAT('%', #{categoryName}, '%')
            </if>
        </where>
        ORDER BY b.created_at DESC
    </select>
    
    <select id="selectOmgBrandsByBrandId" parameterType="Long" resultMap="OmgBrandsResult">
        <include refid="selectOmgBrandsVo"/>
        where brand_id = #{brandId}
    </select>
    <select id="getBrandListByCategoryId" resultType="com.ylkj.system.model.domain.OmgBrands"
            parameterType="java.lang.Long">
        select brand_id, name, slug, logo_url, description, parent_category_id, created_at, updated_at from omg_brands
        where parent_category_id = #{categoryId}
    </select>
    <select id="selectBrandIdByName" resultType="java.lang.Long">
        select brand_id from omg_brands where name = #{secondCategoryName} and parent_category_id = #{categoryId}
    </select>

    <!-- 批量查询品牌ID映射 -->
    <select id="batchSelectBrandIdsByNames" resultType="com.ylkj.system.model.dto.BrandNameIdMapping">
        SELECT
            name as brandName,
            parent_category_id as categoryId,
            brand_id as brandId
        FROM omg_brands
        WHERE (name, parent_category_id) IN
        <foreach collection="brandRequests" item="request" open="(" separator="," close=")">
            (#{request.brandName}, #{request.categoryId})
        </foreach>
    </select>

    <!-- 批量插入品牌（忽略重复） -->
    <insert id="batchInsertBrandsIgnore" parameterType="java.util.List">
        INSERT IGNORE INTO omg_brands (name, parent_category_id, created_at, updated_at)
        VALUES
        <foreach collection="brands" item="brand" separator=",">
            (#{brand.name}, #{brand.parentCategoryId}, NOW(), NOW())
        </foreach>
    </insert>
    <!--    <select id="selectBrandIdByName" resultType="java.lang.Long" parameterType="java.lang.String" >-->
<!--        select brand_id from omg_brands where name = #{secondCategoryName} and parent_category_id = #{categoryId}-->
<!--    </select>-->

    <insert id="insertOmgBrands" parameterType="OmgBrands" useGeneratedKeys="true" keyProperty="brandId">
        insert into omg_brands
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="slug != null and slug != ''">slug,</if>
            <if test="logoUrl != null and logoUrl != ''">logo_url,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="parentCategoryId != null">parent_category_id,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="slug != null and slug != ''">#{slug},</if>
            <if test="logoUrl != null and logoUrl != ''">#{logoUrl},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="parentCategoryId != null">#{parentCategoryId},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>


    <update id="updateOmgBrands" parameterType="OmgBrands">
        update omg_brands
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="slug != null and slug != ''">slug = #{slug},</if>
            <if test="logoUrl != null and logoUrl != ''">logo_url = #{logoUrl},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="parentCategoryId != null">parent_category_id = #{parentCategoryId},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where brand_id = #{brandId}
    </update>

    <delete id="deleteOmgBrandsByBrandId" parameterType="Long">
        delete from omg_brands where brand_id = #{brandId}
    </delete>

    <delete id="deleteOmgBrandsByBrandIds" parameterType="String">
        delete from omg_brands where brand_id in 
        <foreach item="brandId" collection="array" open="(" separator="," close=")">
            #{brandId}
        </foreach>
    </delete>
</mapper>