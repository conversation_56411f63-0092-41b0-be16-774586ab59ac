package com.ylkj.web.controller.system;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.AgtPosts;
import com.ylkj.system.model.vo.agtPosts.AgtPostsVo;
import com.ylkj.system.model.dto.agtPosts.AgtPostsQuery;
import com.ylkj.system.model.dto.agtPosts.AgtPostsInsert;
import com.ylkj.system.model.dto.agtPosts.AgtPostsEdit;
import com.ylkj.system.model.vo.agtPosts.AgtPostDetailVO;
import com.ylkj.system.model.dto.agtPosts.AgtPostDetailEdit;
import com.ylkj.system.service.IAgtPostsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 帖子管理Controller
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/system/posts")
public class AgtPostsController extends BaseController
{
    @Resource
    private IAgtPostsService agtPostsService;

    /**
     * 查询帖子管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:posts:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgtPostsQuery agtPostsQuery)
    {
        AgtPosts agtPosts = AgtPostsQuery.queryToObj(agtPostsQuery);
        startPage();
        List<AgtPosts> list = agtPostsService.selectAgtPostsList(agtPosts);
        List<AgtPostsVo> listVo= list.stream().map(AgtPostsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }
    
    /**
     * 查询帖子详情列表（包含标签、图片、评论）
     */
    @PreAuthorize("@ss.hasPermi('system:posts:list')")
    @GetMapping("/detail/list")
    public TableDataInfo detailList(AgtPostsQuery agtPostsQuery)
    {
        startPage();
        List<AgtPostDetailVO> list = agtPostsService.getPostDetailList(agtPostsQuery);
        TableDataInfo tableDataInfo = getDataTable(list);
        
        // 获取总条数（不分页）
        AgtPosts agtPosts = AgtPostsQuery.queryToObj(agtPostsQuery);
        List<AgtPosts> allPosts = agtPostsService.selectAllAgtPosts(agtPosts);
        if (allPosts != null) {
            tableDataInfo.setTotal(allPosts.size());
        }
        
        return tableDataInfo;
    }

    /**
     * 导出帖子管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:posts:export')")
    @Log(title = "帖子管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtPostsQuery agtPostsQuery)
    {
        AgtPosts agtPosts = AgtPostsQuery.queryToObj(agtPostsQuery);
        List<AgtPosts> list = agtPostsService.selectAgtPostsList(agtPosts);
        ExcelUtil<AgtPosts> util = new ExcelUtil<AgtPosts>(AgtPosts.class);
        util.exportExcel(response, list, "帖子管理数据");
    }

    /**
     * 获取帖子管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:posts:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        AgtPosts agtPosts = agtPostsService.selectAgtPostsById(id);
        return success(AgtPostsVo.objToVo(agtPosts));
    }

    /**
     * 新增帖子管理
     */
    @PreAuthorize("@ss.hasPermi('system:posts:add')")
    @Log(title = "帖子管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Map<String, Object> requestData) {
        return toAjax(agtPostsService.createPost(requestData));
    }

    /**
     * 修改帖子管理
     */
    @PreAuthorize("@ss.hasPermi('system:posts:edit')")
    @Log(title = "帖子管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgtPostsEdit agtPostsEdit)
    {
        AgtPosts agtPosts = AgtPostsEdit.editToObj(agtPostsEdit);
        return toAjax(agtPostsService.updateAgtPosts(agtPosts));
    }

    /**
     * 删除帖子管理
     */
    @PreAuthorize("@ss.hasPermi('system:posts:remove')")
    @Log(title = "帖子管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(agtPostsService.deleteAgtPostsByIds(ids));
    }
    
    /**
     * 获取帖子详情信息（包含标签、图片、评论）
     */
    @PreAuthorize("@ss.hasPermi('system:posts:query')")
    @GetMapping(value = "/detail/{id}")
    public AjaxResult getPostDetail(@PathVariable("id") Long id)
    {
        AgtPostDetailVO detailVO = agtPostsService.getPostDetailById(id);
        return success(detailVO);
    }
    
    /**
     * 更新帖子详情信息（包含标签、图片、评论）
     */
    @PreAuthorize("@ss.hasPermi('system:posts:edit')")
    @Log(title = "帖子详情管理", businessType = BusinessType.UPDATE)
    @PutMapping("/detail")
    public AjaxResult updatePostDetail(@RequestBody AgtPostDetailEdit postDetailEdit)
    {
        return toAjax(agtPostsService.updatePostDetail(postDetailEdit));
    }
    
    /**
     * 删除帖子评论
     */
    @PreAuthorize("@ss.hasPermi('system:posts:edit')")
    @Log(title = "帖子评论管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/comment/{commentId}")
    public AjaxResult deleteComment(@PathVariable("commentId") Long commentId)
    {
        return toAjax(agtPostsService.deletePostComment(commentId));
    }
    
    /**
     * 批量删除帖子评论
     */
    @PreAuthorize("@ss.hasPermi('system:posts:edit')")
    @Log(title = "帖子评论管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/comments/{commentIds}")
    public AjaxResult deleteComments(@PathVariable Long[] commentIds)
    {
        return toAjax(agtPostsService.deletePostCommentBatch(commentIds));
    }
}
