package com.ylkj.system.model.domain;

import java.io.Serializable;
import java.util.Map;
import java.util.Date;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ylkj.common.utils.StringUtils;
import lombok.Data;
import com.ylkj.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

/**
 * 帖子管理对象 agt_posts
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@TableName("agt_posts")
@Data
public class AgtPosts implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 帖子 ID */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 用户 ID */
    @Excel(name = "用户 ID")
    private Long userId;

    /** 点赞数 */
    @Excel(name = "点赞数")
    private Long likes;

    /** 收藏数 */
    @Excel(name = "收藏数")
    private Long favorites;

    /** 浏览量 */
    @Excel(name = "浏览量")
    private Long views;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 图片路径 */
    @Excel(name = "图片路径")
    private String image;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;

    /** 标签列表  */
    @TableField(exist = false)
    private List<String> tags;

    /** 图片URL数组  */
    @TableField(exist = false)
    private String[] imageUrls;

}
