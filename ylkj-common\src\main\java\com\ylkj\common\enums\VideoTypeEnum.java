package com.ylkj.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 视频类型枚举
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public enum VideoTypeEnum {
    
    /**
     * 达人
     */
    TALENT("talent", "达人"),
    
    /**
     * 测评
     */
    REVIEW("review", "测评");
    
    @EnumValue
    private final String code;
    
    private final String desc;
    
    VideoTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    @JsonValue
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static VideoTypeEnum getByCode(String code) {
        for (VideoTypeEnum type : VideoTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据desc获取枚举
     */
    public static VideoTypeEnum getByDesc(String desc) {
        for (VideoTypeEnum type : VideoTypeEnum.values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        return null;
    }
}
