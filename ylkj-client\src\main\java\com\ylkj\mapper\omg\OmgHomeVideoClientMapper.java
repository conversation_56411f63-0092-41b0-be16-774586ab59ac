package com.ylkj.mapper.omg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylkj.common.enums.VideoTypeEnum;
import com.ylkj.model.domain.OmgHomeVideoClient;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * omg首页视频Mapper接口（客户端）
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Mapper
public interface OmgHomeVideoClientMapper extends BaseMapper<OmgHomeVideoClient> {
    
    /**
     * 查询首页视频列表
     *
     * @return 首页视频列表
     */
    List<OmgHomeVideoClient> selectOmgHomeVideoClientList();

    /**
     * 根据类型查询首页视频列表
     *
     * @param type 视频类型
     * @return 首页视频列表
     */
    List<OmgHomeVideoClient> selectOmgHomeVideoClientListByType(@Param("type") VideoTypeEnum type);
} 