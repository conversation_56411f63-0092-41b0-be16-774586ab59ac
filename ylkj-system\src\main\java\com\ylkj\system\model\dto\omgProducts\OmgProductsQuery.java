package com.ylkj.system.model.dto.omgProducts;

import java.util.Map;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ylkj.system.model.domain.OmgProducts;
/**
 * omg_商品Query对象 omg_products
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
public class OmgProductsQuery implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 商品唯一标识 */
    @TableId(value = "product_id", type = IdType.ASSIGN_ID)
    private Long productId;

    /** 关联卖家ID */
    private Long sellerId;

    /** 分类id */
    private Long categoryId;
    /** 商品名称 */
    private String name;

    /** 商品标识符（用于URL） */
    private String slug;

    /** 商品价格 */
    private BigDecimal price;

    /** 商品状态 */
    private String status;

    /** 商品所属商家 */
    private String merchant;

    /** 商品所属平台 */
    private String platform;

    private String video;

    /** 运输天数 */
    private String averageArrival;

    /** 商品重量 */
    private BigDecimal weight;

    /** 商品属性：SP是标品，REP是仿品 */
    private String productAttributes;

    /** 最低价格 */
    private BigDecimal minPrice;

    /** 最高价格 */
    private BigDecimal maxPrice;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;

    /**
     * 对象转封装类
     *
     * @param omgProductsQuery 查询对象
     * @return OmgProducts
     */
    public static OmgProducts queryToObj(OmgProductsQuery omgProductsQuery) {
        if (omgProductsQuery == null) {
            return null;
        }
        OmgProducts omgProducts = new OmgProducts();
        BeanUtils.copyProperties(omgProductsQuery, omgProducts);
        return omgProducts;
    }
}
