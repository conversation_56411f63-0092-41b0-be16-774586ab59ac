<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.mapper.omg.OmgHomeVideoClientMapper">
    
    <resultMap type="com.ylkj.model.domain.OmgHomeVideoClient" id="OmgHomeVideoClientResult">
        <id property="id" column="id" />
        <result property="videoUrl" column="videoUrl" />
        <result property="avatar" column="avatar" />
        <result property="username" column="username" />
        <result property="description" column="description" />
        <result property="type" column="type" />
    </resultMap>
    
    <sql id="selectOmgHomeVideoClientVo">
        select id, videoUrl, avatar, username, description, type
        from omg_home_video
    </sql>
    
    <!-- 查询首页视频列表 -->
    <select id="selectOmgHomeVideoClientList" resultMap="OmgHomeVideoClientResult">
        <include refid="selectOmgHomeVideoClientVo"/>
    </select>

    <!-- 根据类型查询首页视频列表 -->
    <select id="selectOmgHomeVideoClientListByType" resultMap="OmgHomeVideoClientResult">
        <include refid="selectOmgHomeVideoClientVo"/>
        WHERE type = #{type}
    </select>

</mapper>