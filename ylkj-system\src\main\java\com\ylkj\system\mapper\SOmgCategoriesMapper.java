package com.ylkj.system.mapper;

import java.util.List;
import com.ylkj.system.model.domain.SOmgCategories;
import com.ylkj.system.model.dto.CategoryNameIdMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * omg_分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
public interface SOmgCategoriesMapper extends BaseMapper<SOmgCategories>
{
    /**
     * 查询omg_分类
     * 
     * @param categoryId omg_分类主键
     * @return omg_分类
     */
    public SOmgCategories selectOmgCategoriesByCategoryId(Long categoryId);

    /**
     * 查询omg_分类列表
     * 
     * @param omgCategories omg_分类
     * @return omg_分类集合
     */
    public List<SOmgCategories> selectOmgCategoriesList(SOmgCategories omgCategories);

    /**
     * 新增omg_分类
     * 
     * @param omgCategories omg_分类
     * @return 结果
     */
    public int insertOmgCategories(SOmgCategories omgCategories);

    /**
     * 修改omg_分类
     * 
     * @param omgCategories omg_分类
     * @return 结果
     */
    public int updateOmgCategories(SOmgCategories omgCategories);

    /**
     * 删除omg_分类
     * 
     * @param categoryId omg_分类主键
     * @return 结果
     */
    public int deleteOmgCategoriesByCategoryId(Long categoryId);

    /**
     * 批量删除omg_分类
     * 
     * @param categoryIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOmgCategoriesByCategoryIds(Long[] categoryIds);

    /**
     * 根据分类名称查询分类ID
     * @param categoryName
     * @return
     */
    Long selectCategoryIdByName(String categoryName);

    /**
     * 批量查询分类ID映射
     * @param categoryNames 分类名称列表
     * @return 分类名称到ID的映射列表
     */
    List<CategoryNameIdMapping> batchSelectCategoryIdsByNames(@Param("categoryNames") List<String> categoryNames);

    /**
     * 批量插入分类（使用INSERT IGNORE避免重复）
     * @param categories 分类列表
     * @return 插入成功的记录数
     */
    int batchInsertCategoriesIgnore(@Param("categories") List<SOmgCategories> categories);
}
