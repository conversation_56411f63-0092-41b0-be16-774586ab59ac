package com.ylkj.system.model.dto.omgHomeVideo;

import java.io.Serializable;

import com.ylkj.common.enums.VideoTypeEnum;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import com.ylkj.system.model.domain.OmgHomeVideo;
/**
 * omg首页视频组Vo对象 omg_home_video
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Data
public class OmgHomeVideoInsert implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 达人视频表id */
    private Long id;

    /** 视频URL */
    private String videoUrl;

    /** 头像 */
    private String avatar;

    /** 名称 */
    private String username;

    /** 内容 */
    private String description;

    /** 视频类型 */
    private VideoTypeEnum type;

    /**
     * 对象转封装类
     *
     * @param omgHomeVideoInsert 插入对象
     * @return OmgHomeVideoInsert
     */
    public static OmgHomeVideo insertToObj(OmgHomeVideoInsert omgHomeVideoInsert) {
        if (omgHomeVideoInsert == null) {
            return null;
        }
        OmgHomeVideo omgHomeVideo = new OmgHomeVideo();
        BeanUtils.copyProperties(omgHomeVideoInsert, omgHomeVideo);
        return omgHomeVideo;
    }
}
