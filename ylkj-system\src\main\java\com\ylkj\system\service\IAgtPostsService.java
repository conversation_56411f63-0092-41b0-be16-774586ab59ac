package com.ylkj.system.service;

import java.util.List;
import java.util.Map;
import com.ylkj.system.model.domain.AgtPosts;
import com.ylkj.system.model.vo.agtPosts.AgtPostsVo;
import com.ylkj.system.model.dto.agtPosts.AgtPostsQuery;
import com.ylkj.system.model.vo.agtPosts.AgtPostDetailVO;
import com.ylkj.system.model.dto.agtPosts.AgtPostDetailEdit;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
/**
 * 帖子管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface IAgtPostsService extends IService<AgtPosts>
{
    //region mybatis代码
    /**
     * 查询帖子管理
     * 
     * @param id 帖子管理主键
     * @return 帖子管理
     */
    public AgtPosts selectAgtPostsById(Long id);

    /**
     * 查询帖子管理列表
     * 
     * @param agtPosts 帖子管理
     * @return 帖子管理集合
     */
    public List<AgtPosts> selectAgtPostsList(AgtPosts agtPosts);

    /**
     * 新增帖子管理
     * 
     * @param agtPosts 帖子管理
     * @return 结果
     */
    public int insertAgtPosts(AgtPosts agtPosts);

    /**
     * 修改帖子管理
     * 
     * @param agtPosts 帖子管理
     * @return 结果
     */
    public int updateAgtPosts(AgtPosts agtPosts);

    /**
     * 批量删除帖子管理
     * 
     * @param ids 需要删除的帖子管理主键集合
     * @return 结果
     */
    public int deleteAgtPostsByIds(Long[] ids);

    /**
     * 删除帖子管理信息
     * 
     * @param id 帖子管理主键
     * @return 结果
     */
    public int deleteAgtPostsById(Long id);

    /**
     * 获取所有帖子（不分页）
     * 
     * @param agtPosts 查询条件
     * @return 帖子列表
     */
    public List<AgtPosts> selectAllAgtPosts(AgtPosts agtPosts);
    //endregion

    /**
     * 获取查询条件
     *
     * @param agtPostsQuery 查询条件对象
     * @return 查询条件
     */
    QueryWrapper<AgtPosts> getQueryWrapper(AgtPostsQuery agtPostsQuery);

    /**
     * 转换vo
     *
     * @param agtPostsList AgtPosts集合
     * @return AgtPostsVO集合
     */
    List<AgtPostsVo> convertVoList(List<AgtPosts> agtPostsList);
    
    /**
     * 获取帖子详情，包括标签、图片、评论等
     *
     * @param id 帖子ID
     * @return 帖子详情VO
     */
    AgtPostDetailVO getPostDetailById(Long id);
    
    /**
     * 获取帖子详情列表，包括标签、图片、评论等
     *
     * @param agtPostsQuery 查询条件
     * @return 帖子详情VO列表
     */
    List<AgtPostDetailVO> getPostDetailList(AgtPostsQuery agtPostsQuery);
    
    /**
     * 更新帖子详情，包括标签、图片、评论等
     *
     * @param postDetailEdit 帖子详情编辑对象
     * @return 是否成功
     */
    boolean updatePostDetail(AgtPostDetailEdit postDetailEdit);
    
    /**
     * 删除帖子评论
     *
     * @param commentId 评论ID
     * @return 是否成功
     */
    boolean deletePostComment(Long commentId);
    
    /**
     * 批量删除帖子评论
     *
     * @param commentIds 评论ID数组
     * @return 是否成功
     */
    boolean deletePostCommentBatch(Long[] commentIds);

    /**
     * 创建帖子（包含标签和图片处理）
     * @param requestData 请求数据
     * @return 创建结果
     */
    int createPost(Map<String, Object> requestData);
}
