<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.system.mapper.OmgHomeVideoMapper">
    
    <resultMap type="OmgHomeVideo" id="OmgHomeVideoResult">
        <result property="id"    column="id"    />
        <result property="videoUrl"    column="videoUrl"    />
        <result property="avatar"    column="avatar"    />
        <result property="username"    column="username"    />
        <result property="description"    column="description"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectOmgHomeVideoVo">
        select id, videoUrl, avatar, username, description, type from omg_home_video
    </sql>

    <select id="selectOmgHomeVideoList" parameterType="OmgHomeVideo" resultMap="OmgHomeVideoResult">
        <include refid="selectOmgHomeVideoVo"/>
        <where>
            <if test="videoUrl != null  and videoUrl != ''"> and videoUrl = #{videoUrl}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="username != null  and username != ''"> and username like concat('%', #{username}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="type != null"> and type = #{type}</if>
        </where>
    </select>
    
    <select id="selectOmgHomeVideoById" parameterType="Long" resultMap="OmgHomeVideoResult">
        <include refid="selectOmgHomeVideoVo"/>
        where id = #{id}
    </select>

    <insert id="insertOmgHomeVideo" parameterType="OmgHomeVideo" useGeneratedKeys="true" keyProperty="id">
        insert into omg_home_video
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="videoUrl != null">videoUrl,</if>
            <if test="avatar != null">avatar,</if>
            <if test="username != null">username,</if>
            <if test="description != null">description,</if>
            <if test="type != null">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="videoUrl != null">#{videoUrl},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="username != null">#{username},</if>
            <if test="description != null">#{description},</if>
            <if test="type != null">#{type},</if>
         </trim>
    </insert>

    <update id="updateOmgHomeVideo" parameterType="OmgHomeVideo">
        update omg_home_video
        <trim prefix="SET" suffixOverrides=",">
            <if test="videoUrl != null">videoUrl = #{videoUrl},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="username != null">username = #{username},</if>
            <if test="description != null">description = #{description},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOmgHomeVideoById" parameterType="Long">
        delete from omg_home_video where id = #{id}
    </delete>

    <delete id="deleteOmgHomeVideoByIds" parameterType="String">
        delete from omg_home_video where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>