<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.mapper.UsersMapper">

    <insert id="register" useGeneratedKeys="true" keyProperty="userId">
        insert into agt_users (username, password, first_name, last_name, phone_number, email, created_at, updated_at,currency,avatar_url)
        values (#{username}, #{password},#{firstName},#{lastName}, #{phoneNumber}, #{email}, now(), now(), #{currency},#{avatarUrl})
    </insert>

    <select id="login" resultType="com.ylkj.model.domain.Users">
        select *
        from agt_users
        where username = #{username}
          and password = #{password}
    </select>

    <select id="selectByUsername" resultType="com.ylkj.model.domain.Users">
        select *
        from agt_users
        where username = #{username}
    </select>

    <select id="selectUserByEmail" resultType="com.ylkj.model.domain.Users">
        select *
        from agt_users
        where email = #{email}
    </select>

    <update id="ChangeUserInformation">
        UPDATE agt_users
        <set>
            <if test="username != null">username = #{username},</if>
            <if test="lastName != null">last_name = #{lastName},</if>
            <if test="firstName != null">first_name = #{firstName},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl}</if>
        </set>
        WHERE email = #{email}
    </update>

    <update id="updatePasswordByEmail">
        UPDATE agt_users
        SET password = #{newPassword}
        WHERE email = #{email};
    </update>


    <!--   更新用户最后登陆时间 -->
    <update id="updateLastLoginAtInt">
        update agt_users set last_login_at = now() where email = #{email}
    </update>



</mapper>
