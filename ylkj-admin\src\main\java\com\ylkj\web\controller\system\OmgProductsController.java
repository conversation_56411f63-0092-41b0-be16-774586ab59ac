package com.ylkj.web.controller.system;


import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;


import com.github.pagehelper.PageInfo;
import com.ylkj.model.domain.omg.FindQcImageProcessResult;
import com.ylkj.service.impl.OkHttpGoodsService;
import com.ylkj.service.FindQcImageProcessService;
import com.ylkj.system.model.domain.*;
import com.ylkj.system.model.vo.agtProducts.UpdateStatusVo;
import com.ylkj.system.model.vo.omgProducts.ImportResultVo;
import com.ylkj.system.service.AsyncImportService;
import com.ylkj.system.service.IOmgProductMainImagesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.vo.omgProducts.OmgProductsVo;
import com.ylkj.system.model.dto.omgProducts.OmgProductsQuery;
import com.ylkj.system.model.dto.omgProducts.OmgProductsInsert;
import com.ylkj.system.model.dto.omgProducts.OmgProductsEdit;
import com.ylkj.system.model.dto.omgProducts.MainImageOperationDTO;
import com.ylkj.system.service.IOmgProductsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

/**
 * omg_商品Controller
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Slf4j
@RestController
@RequestMapping("/system/OmgProducts")
public class OmgProductsController extends BaseController {
    @Resource
    private IOmgProductsService omgProductsService;

    @Autowired
    private AsyncImportService asyncImportService;

    @Autowired
    private OkHttpGoodsService okHttpGoodsService;
    @Autowired
    private FindQcImageProcessService findQcImageProcessService;
    @Autowired
    private IOmgProductMainImagesService omgProductMainImagesService;

    /**
     * 查询omg_商品列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgProductsQuery omgProductsQuery) {
        OmgProducts omgProducts = OmgProductsQuery.queryToObj(omgProductsQuery);
        startPage();
        List<OmgProducts> list = omgProductsService.selectOmgProductsList(omgProducts);
        List<OmgProductsVo> listVo = list.stream().map(OmgProductsVo::objToVo).collect(Collectors.toList());

        //判断主图是否存在
        listVo.forEach(vo -> {
            //判断商品是否为上架
            if (vo.getStatus().equals("active")) {
                if (vo.getMainImage()== null || vo.getMainImage().equals(" ")|| vo.getMainImage().equals("")) {
                    // 通过sku从商品主图表中获取商品主图
                    OmgProductMainImages mainImage = omgProductMainImagesService.selectMainImageBySku(vo.getSku());
                    if (mainImage != null) {
                        vo.setMainImage(mainImage.getOssImageUrl());
                    }else {
                        getMainImageById(vo);
                    }
                }
            }

        });
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出omg_商品列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:export')")
    @Log(title = "omg_商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgProductsQuery omgProductsQuery) {
        OmgProducts omgProducts = OmgProductsQuery.queryToObj(omgProductsQuery);
        List<OmgProducts> list = omgProductsService.selectOmgProductsList(omgProducts);
        ExcelUtil<OmgProducts> util = new ExcelUtil<OmgProducts>(OmgProducts.class);
        util.exportExcel(response, list, "omg_商品数据");
    }

    /**
     * 获取omg_商品详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:query')")
    @GetMapping(value = "/{productId}")
    public AjaxResult getInfo(@PathVariable("productId") Long productId) {
        OmgProducts omgProducts = omgProductsService.selectOmgProductsByProductId(productId);
        return success(OmgProductsVo.objToVo(omgProducts));
    }

    /**
     * 检查SKU是否存在
     */
    @GetMapping("/checkSku/{sku}")
    public AjaxResult checkSku(@PathVariable("sku") String sku) {
        boolean exists = omgProductsService.isSkuExists(sku);
        Map<String, Object> result = new HashMap<>();
        result.put("sku", sku);
        result.put("exists", exists);
        if (exists) {
            OmgProducts product = omgProductsService.selectOmgProductsBySku(sku);
            result.put("product", product);
        }
        return success(result);
    }

    /**
     * 批量检查SKU是否存在（测试接口）
     */
    @PostMapping("/checkSkusBatch")
    public AjaxResult checkSkusBatch(@RequestBody List<String> skuList) {
        try {
            if (skuList == null || skuList.isEmpty()) {
                return AjaxResult.error("SKU列表不能为空");
            }

            List<String> existingSkus = omgProductsService.checkSkusExist(skuList);

            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", skuList.size());
            result.put("existingCount", existingSkus.size());
            result.put("existingSkus", existingSkus);
            result.put("newSkus", skuList.stream()
                    .filter(sku -> !existingSkus.contains(sku))
                    .collect(java.util.stream.Collectors.toList()));

            return success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("批量检查SKU失败：" + e.getMessage());
        }
    }

    /**
     * 新增omg_商品
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:add')")
    @Log(title = "omg_商品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgProductsInsert omgProductsInsert) {
        OmgProducts omgProducts = OmgProductsInsert.insertToObj(omgProductsInsert);
        return toAjax(omgProductsService.insertOmgProducts(omgProducts));
    }

    /**
     * 修改omg_商品
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:edit')")
    @Log(title = "omg_商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgProductsEdit omgProductsEdit) {
        OmgProducts omgProducts = OmgProductsEdit.editToObj(omgProductsEdit);
        return toAjax(omgProductsService.updateOmgProducts(omgProducts));
    }

    /**
     * 删除omg_商品
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:remove')")
    @Log(title = "omg_商品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{productIds}")
    public AjaxResult remove(@PathVariable Long[] productIds) {
        return toAjax(omgProductsService.deleteOmgProductsByProductIds(productIds));
    }

    /**
     * 修改omg_商品状态
     */
    @PreAuthorize("@ss.hasPermi('system:products:edit')")
    @Log(title = "omg_商品管理", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateProductStatus(@RequestBody UpdateStatusVo vo) {
        return toAjax(omgProductsService.updateOmgProductsStatus(vo));
    }

    /**
     * 设置商品主图
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:edit')")
    @Log(title = "设置商品主图", businessType = BusinessType.UPDATE)
    @PostMapping("/setMainImage")
    public AjaxResult setMainImage(@RequestBody MainImageOperationDTO operationDTO) {
        try {
            // 参数验证
            if (operationDTO == null || !operationDTO.isValidForSingleOperation()) {
                return error("参数错误：图片ID不能为空");
            }

            // 获取图片信息
            OmgProductMainImages existingImage = omgProductMainImagesService.selectOmgProductMainImagesById(operationDTO.getImageId());
            if (existingImage == null) {
                return error("图片不存在，ID: " + operationDTO.getImageId());
            }

            // 检查图片状态
            if (!"active".equals(existingImage.getStatus())) {
                return error("图片状态异常，无法设置为主图");
            }

            // 设置主图
            int result = omgProductMainImagesService.setMainImage(existingImage.getSku(), operationDTO.getImageId());

            if (result > 0) {
                log.info("设置主图成功，SKU: {}, 图片ID: {}, 操作者: {}",
                        existingImage.getSku(), operationDTO.getImageId(), operationDTO.getOperator());
                return success("设置主图成功");
            } else {
                return error("设置主图失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("设置主图异常，图片ID: {}", operationDTO != null ? operationDTO.getImageId() : "null", e);
            return error("设置主图失败：" + e.getMessage());
        }
    }

    /**
     * 取消商品主图
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:edit')")
    @Log(title = "取消商品主图", businessType = BusinessType.UPDATE)
    @PostMapping("/cancelMainImage")
    public AjaxResult cancelMainImage(@RequestBody MainImageOperationDTO operationDTO) {
        try {
            // 参数验证
            if (operationDTO == null || !operationDTO.isValidForSingleOperation()) {
                return error("参数错误：图片ID不能为空");
            }

            // 获取图片信息
            OmgProductMainImages existingImage = omgProductMainImagesService.selectOmgProductMainImagesById(operationDTO.getImageId());
            if (existingImage == null) {
                return error("图片不存在，ID: " + operationDTO.getImageId());
            }

            // 检查是否为主图
            if (existingImage.getIsMain() == null || existingImage.getIsMain() != 1) {
                if (operationDTO.getForceOperation() != null && operationDTO.getForceOperation()) {
                    log.warn("强制取消主图操作，图片ID: {} 不是主图", operationDTO.getImageId());
                } else {
                    return error("该图片不是主图，无需取消");
                }
            }

            // 取消主图标记（清除该SKU的所有主图标记）
            int result = omgProductMainImagesService.clearMainImageBySku(existingImage.getSku());

            if (result >= 0) { // >= 0 因为可能没有主图需要清除
                log.info("取消主图成功，SKU: {}, 图片ID: {}, 操作者: {}",
                        existingImage.getSku(), operationDTO.getImageId(), operationDTO.getOperator());
                return success("取消主图成功");
            } else {
                return error("取消主图失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("取消主图异常，图片ID: {}", operationDTO != null ? operationDTO.getImageId() : "null", e);
            return error("取消主图失败：" + e.getMessage());
        }
    }

    /**
     * 批量设置主图
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:edit')")
    @Log(title = "批量设置商品主图", businessType = BusinessType.UPDATE)
    @PostMapping("/batchSetMainImage")
    public AjaxResult batchSetMainImage(@RequestBody MainImageOperationDTO operationDTO) {
        try {
            // 参数验证
            if (operationDTO == null || !operationDTO.isValidForBatchOperation()) {
                return error("参数错误：图片ID列表不能为空");
            }

            List<Long> imageIds = operationDTO.getImageIds();
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMsg = new StringBuilder();

            for (Long imageId : imageIds) {
                try {
                    if (imageId == null) {
                        failCount++;
                        errorMsg.append("图片ID不能为空; ");
                        continue;
                    }

                    // 获取图片信息
                    OmgProductMainImages existingImage = omgProductMainImagesService.selectOmgProductMainImagesById(imageId);
                    if (existingImage == null) {
                        failCount++;
                        errorMsg.append("图片ID[").append(imageId).append("]不存在; ");
                        continue;
                    }

                    // 检查图片状态
                    if (!"active".equals(existingImage.getStatus())) {
                        failCount++;
                        errorMsg.append("图片ID[").append(imageId).append("]状态异常; ");
                        continue;
                    }

                    // 设置主图
                    int result = omgProductMainImagesService.setMainImage(existingImage.getSku(), imageId);
                    if (result > 0) {
                        successCount++;
                        log.debug("批量设置主图成功，SKU: {}, 图片ID: {}", existingImage.getSku(), imageId);
                    } else {
                        failCount++;
                        errorMsg.append("SKU[").append(existingImage.getSku()).append("]设置失败; ");
                    }
                } catch (Exception e) {
                    failCount++;
                    errorMsg.append("处理图片ID[").append(imageId).append("]异常: ").append(e.getMessage()).append("; ");
                    log.error("批量设置主图单个操作异常，图片ID: {}", imageId, e);
                }
            }

            String resultMsg = String.format("批量设置主图完成，成功: %d, 失败: %d", successCount, failCount);
            if (failCount > 0) {
                resultMsg += "，失败原因: " + errorMsg.toString();
            }

            log.info("批量设置主图结果: {}，操作者: {}", resultMsg, operationDTO.getOperator());

            if (successCount > 0) {
                return success(resultMsg);
            } else {
                return error(resultMsg);
            }
        } catch (Exception e) {
            log.error("批量设置主图异常", e);
            return error("批量设置主图失败：" + e.getMessage());
        }
    }

    /**
     * 根据SKU获取商品主图列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:query')")
    @GetMapping("/mainImages/{sku}")
    public AjaxResult getMainImagesBySku(@PathVariable String sku) {
        try {
            if (sku == null || sku.trim().isEmpty()) {
                return error("SKU不能为空");
            }

            List<OmgProductMainImages> imagesList = omgProductMainImagesService.selectImagesBySku(sku);
            return success(imagesList);
        } catch (Exception e) {
            log.error("获取商品主图列表异常，SKU: {}", sku, e);
            return error("获取商品主图列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据SKU获取当前主图
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:query')")
    @GetMapping("/currentMainImage/{sku}")
    public AjaxResult getCurrentMainImageBySku(@PathVariable String sku) {
        try {
            if (sku == null || sku.trim().isEmpty()) {
                return error("SKU不能为空");
            }

            OmgProductMainImages mainImage = omgProductMainImagesService.selectMainImageBySku(sku);
            return success(mainImage);
        } catch (Exception e) {
            log.error("获取当前主图异常，SKU: {}", sku, e);
            return error("获取当前主图失败：" + e.getMessage());
        }
    }

    /**
     * @param response
     * @author: 小许
     * @date: 2025/5/28/周三 17:32
     * @description: 导出下载模版
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<OmgProducts> util = new ExcelUtil<>(OmgProducts.class);
        util.importTemplateExcel(response, "omg_商品数据", "omg_商品数据");
    }

    /**
     * @author: 小许
     * @date: 2025/5/28/周三 19:27
     * @description: 批量导入数据
     * @param file
     * @return AjaxResult
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:export')")
    @Log(title = "omg_商品", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) {
        try {
            ExcelUtil<OmgProducts> util = new ExcelUtil<>(OmgProducts.class);
            // 跳过第一行（大标题），第二行为表头，第三行开始为数据
            List<OmgProducts> omgProductsList = util.importExcel(file.getInputStream(), 1);
            System.err.println("准备导入商品数量: " + omgProductsList.size());

            // 根据数据量选择导入方式
           ImportResultVo importResult;

            if (omgProductsList.size() > 200) {
                // 大数据量使用批量导入
                System.out.println("检测到大数据量(" + omgProductsList.size() + ")，使用批量导入模式");
                importResult = omgProductsService.importOmgProductsBatch(omgProductsList, 100);
            } else {
                // 小数据量使用普通导入
                importResult = omgProductsService.importOmgProducts(omgProductsList);
            }

            if (importResult.getSuccessCount() > 0) {
                // 导入成功后更新商品链接
                updateTaobaoProductsUrl();
                update1688ProductsUrl();
                updateWeidianProductsUrl();
            }

            // 返回详细的导入结果
            if (importResult.isSuccess()) {
                return AjaxResult.success(importResult.getMessage(), importResult);
            } else {
                return AjaxResult.error(importResult.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 大数据量商品导入
     */
    @Log(title = "大数据量商品导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importDataBatch")
    public AjaxResult importDataBatch(MultipartFile file, @RequestParam(defaultValue = "200") int batchSize) {
        try {
            if (batchSize <= 0 || batchSize > 2000) {
                return AjaxResult.error("批处理大小必须在1-2000之间");
            }

            ExcelUtil<OmgProducts> util = new ExcelUtil<>(OmgProducts.class);
            List<OmgProducts> omgProductsList = util.importExcel(file.getInputStream(), 1);

            System.out.println("=== 大数据量导入开始 ===");
            System.out.println("商品总数: " + omgProductsList.size());
            System.out.println("批处理大小: " + batchSize);

            // 使用批量导入
            ImportResultVo importResult = omgProductsService.importOmgProductsBatch(omgProductsList, batchSize);

            if (importResult.getSuccessCount() > 0) {
                // 导入成功后更新商品链接
                updateTaobaoProductsUrl();
                update1688ProductsUrl();
                updateWeidianProductsUrl();
            }

            return AjaxResult.success(importResult.getMessage(), importResult);

        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("大数据量导入失败：" + e.getMessage());
        }
    }

    /**
     * 异步大数据量商品导入
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:export')")
    @Log(title = "异步商品导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importDataAsync")
    public AjaxResult importDataAsync(MultipartFile file,
                                     @RequestParam(defaultValue = "500") int batchSize) {
        try {
            if (batchSize <= 0 || batchSize > 2000) {
                return AjaxResult.error("批处理大小必须在1-2000之间");
            }

            ExcelUtil<OmgProducts> util = new ExcelUtil<>(OmgProducts.class);
            List<OmgProducts> omgProductsList = util.importExcel(file.getInputStream(), 1);

            // 生成任务ID
            String taskId = asyncImportService.generateTaskId();

            // 提交异步任务
            asyncImportService.importProductsAsync(omgProductsList, batchSize, taskId);

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("totalCount", omgProductsList.size());
            result.put("message", "异步导入任务已提交，请使用taskId查询进度");

            return AjaxResult.success("异步导入任务已启动", result);

        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("提交异步导入任务失败：" + e.getMessage());
        }
    }

    /**
     * 查询异步导入任务状态
     */
    @GetMapping("/importStatus/{taskId}")
    public AjaxResult getImportStatus(@PathVariable String taskId) {
        AsyncImportService.ImportTaskStatus status = asyncImportService.getTaskStatus(taskId);
        if (status == null) {
            return AjaxResult.error("任务不存在或已过期");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("taskId", status.getTaskId());
        result.put("status", status.getStatus());
        result.put("progress", status.getProgress());
        result.put("message", status.getMessage());
        result.put("duration", status.getDuration());

        if ("COMPLETED".equals(status.getStatus()) && status.getResult() != null) {
            result.put("importResult", status.getResult());
        }

        return AjaxResult.success(result);
    }

    /**
     * 获取所有异步导入任务状态
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:query')")
    @GetMapping("/importStatus/all")
    public AjaxResult getAllImportStatus() {
        Map<String, AsyncImportService.ImportTaskStatus> allStatus = asyncImportService.getAllTaskStatus();
        return AjaxResult.success(allStatus);
    }

    /**
    * @Author: 吴居乐
    * @Description: 根据id获取商品主图，并更新数据库中主图信息
    * @DateTime: 15:41 2025/6/17/周二
    * @Params:productId
    * @Return
    */
    public void getMainImageById(OmgProductsVo omgProducts) {
        //通过平台判断获取商品主图
        String platform = omgProducts.getPlatform();
        //使用switch进行匹配,有淘宝,1688,微店三种平台
        String mainImage= "";

        // 新增：调用FindQC图片处理服务
        try {
            String mallType = getMallTypeByPlatform(platform);
            if (mallType != null) {
                log.info("开始调用FindQC图片处理服务，SKU: {}, 平台: {}, mallType: {}",
                        omgProducts.getSku(), platform, mallType);

                FindQcImageProcessResult result =
findQcImageProcessService.processProductImages(
                                omgProducts.getSku(),
                                mallType,
                                "USD",
                                "en");

                if (result != null && result.isSuccess() &&
                    result.getPicListOssUrls() != null && !result.getPicListOssUrls().isEmpty()) {
                    // 使用FindQC处理后的第一张OSS图片作为主图
                    mainImage = result.getPicListOssUrls().get(0);
                    omgProducts.setMainImage(mainImage);
                    log.info("FindQC图片处理成功，获取到主图: {}", mainImage);
                } else {
                    log.warn("FindQC图片处理失败或无图片，SKU: {}, 使用原有逻辑获取主图", omgProducts.getSku());
                    // 如果FindQC处理失败，使用原有逻辑
                    mainImage = getMainImageByOriginalLogic(omgProducts, platform);
                }
            } else {
                log.warn("平台 {} 不支持FindQC处理，使用原有逻辑", platform);
                // 不支持的平台使用原有逻辑
                mainImage = getMainImageByOriginalLogic(omgProducts, platform);
            }
        } catch (Exception e) {
            log.error("调用FindQC图片处理服务异常，SKU: {}, 使用原有逻辑", omgProducts.getSku(), e);
            // 异常情况下使用原有逻辑
            mainImage = getMainImageByOriginalLogic(omgProducts, platform);
        }

        // 更新数据库中的主图信息
        OmgProducts product = omgProductsService.selectOmgProductsByProductId(omgProducts.getProductId());
//        product.setMainImage(mainImage);
        omgProductsService.updateOmgProducts(product);
    }

    /**
     * 根据平台名称获取FindQC对应的mallType
     */
    private String getMallTypeByPlatform(String platform) {
        if (platform == null) {
            return null;
        }
        switch (platform) {
            case "淘宝":
                return "TAOBAO";
            case "1688":
                return "T1688";
            case "weidian":
            case "微店":
                return "WEIDIAN";
            default:
                return null;
        }
    }

    /**
     * 使用原有逻辑获取主图
     */
    private String getMainImageByOriginalLogic(OmgProductsVo omgProducts, String platform) {
        String mainImage = "";
        switch (platform) {
            case "淘宝":
                 mainImage = okHttpGoodsService.getGoodsDetail(omgProducts.getSku(), "TB","en", "USD").getPicUrl();
                omgProducts.setMainImage(mainImage);
                break;
            case "1688":
                mainImage = okHttpGoodsService.getGoodsDetail(omgProducts.getSku(), "T1688","en", "USD").getPicUrl();
                omgProducts.setMainImage(mainImage);
                break;
            case "weidian":
            case "微店":
                mainImage = okHttpGoodsService.getGoodsDetail(omgProducts.getSku(), "WD","en", "USD").getPicUrl();
                omgProducts.setMainImage(mainImage);
                break;
        }
        return mainImage;
    }

    /**
     * @Author: 系统
     * @Description: 更新淘宝商品链接
     * @DateTime: 2025/7/16
     * @Return 更新结果
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:edit')")
    @Log(title = "更新淘宝商品链接", businessType = BusinessType.UPDATE)
    @PutMapping("/updateTaobaoUrl")
    public AjaxResult updateTaobaoProductsUrl() {
        return toAjax(omgProductsService.updateTaobaoProductsUrl());
    }

    /**
     * @Author: 系统
     * @Description: 更新1688商品链接
     * @DateTime: 2025/7/16
     * @Return 更新结果
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:edit')")
    @Log(title = "更新1688商品链接", businessType = BusinessType.UPDATE)
    @PutMapping("/update1688Url")
    public AjaxResult update1688ProductsUrl() {
        return toAjax(omgProductsService.update1688ProductsUrl());
    }

    /**
     * @Author: 系统
     * @Description: 更新微店商品链接
     * @DateTime: 2025/7/16
     * @Return 更新结果
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:edit')")
    @Log(title = "更新微店商品链接", businessType = BusinessType.UPDATE)
    @PutMapping("/updateWeidianUrl")
    public AjaxResult updateWeidianProductsUrl() {
        return toAjax(omgProductsService.updateWeidianProductsUrl());
    }

    /**
     * @Author: 系统
     * @Description: 通过SKU更新主图表，没有记录则重新获取并更新到OSS和数据库
     * @DateTime: 2025/1/19
     * @Params: sku 商品SKU
     * @Return 更新结果
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:edit')")
    @Log(title = "更新商品主图", businessType = BusinessType.UPDATE)
    @PostMapping("/updateMainImagesBySku/{sku}")
    public AjaxResult updateMainImagesBySku(@PathVariable String sku) {
        try {
            log.info("开始通过SKU更新主图表，SKU: {}", sku);

            // 1. 根据SKU查询商品信息
            OmgProducts product = omgProductsService.selectOmgProductsBySku(sku);
            if (product == null) {
                log.warn("未找到SKU为{}的商品", sku);
                return AjaxResult.error("未找到对应的商品信息");
            }

            // 2. 检查主图表中是否已有记录
            int existingCount = omgProductMainImagesService.countImagesBySku(sku);
            log.info("SKU: {} 在主图表中已有 {} 条记录", sku, existingCount);

            if (existingCount > 0) {
                // 已有记录，返回现有图片信息
                List<OmgProductMainImages> existingImages = omgProductMainImagesService.selectImagesBySku(sku);
                log.info("SKU: {} 已有主图记录，无需重新获取", sku);
                return AjaxResult.success("商品已有主图记录", existingImages);
            }

            // 3. 没有记录，根据平台重新获取图片
            String platform = product.getPlatform();
            String mallType = getMallTypeByPlatform(platform);

            if (mallType == null) {
                log.warn("平台 {} 不支持FindQC处理，SKU: {}", platform, sku);
                return AjaxResult.error("不支持的商品平台: " + platform);
            }

            // 4. 调用FindQC图片处理服务
            FindQcImageProcessResult result =
                    findQcImageProcessService.processProductImages(sku, mallType, "USD", "en");

            if (result == null || !result.isSuccess()) {
                log.error("FindQC图片处理失败，SKU: {}", sku);
                return AjaxResult.error("图片处理失败: " + (result != null ? result.getMessage() : "未知错误"));
            }

            // 5. 检查处理结果
            if (result.getPicListOssUrls() == null || result.getPicListOssUrls().isEmpty()) {
                log.warn("FindQC处理成功但未获取到图片，SKU: {}", sku);
                return AjaxResult.error("未获取到商品图片");
            }

            // 6. 更新商品表中的主图字段
            String mainImageUrl = result.getPicListOssUrls().get(0);
            product.setMainImage(mainImageUrl);
            omgProductsService.updateOmgProducts(product);

            // 7. 查询保存到主图表的记录
            List<OmgProductMainImages> savedImages = omgProductMainImagesService.selectImagesBySku(sku);

            log.info("SKU: {} 主图更新完成，主图URL: {}, 保存图片数量: {}",
                    sku, mainImageUrl, savedImages.size());

            return AjaxResult.success("主图更新成功")
                    .put("mainImageUrl", mainImageUrl)
                    .put("totalImages", savedImages.size())
                    .put("imageList", savedImages)
                    .put("processResult", result);

        } catch (Exception e) {
            log.error("通过SKU更新主图表异常，SKU: {}", sku, e);
            return AjaxResult.error("更新主图异常: " + e.getMessage());
        }
    }

    /**
     * @Author: 系统
     * @Description: 批量通过SKU更新主图表
     * @DateTime: 2025/1/19
     * @Params: skuList SKU列表
     * @Return 批量更新结果
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:edit')")
    @Log(title = "批量更新商品主图", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateMainImagesBySku")
    public AjaxResult batchUpdateMainImagesBySku(@RequestBody List<String> skuList) {
        try {
            log.info("开始批量更新主图表，SKU数量: {}", skuList.size());

            BatchUpdateResult batchResult = new BatchUpdateResult();
            batchResult.setTotal(skuList.size());

            for (String sku : skuList) {
                try {
                    // 检查是否已有记录
                    int existingCount = omgProductMainImagesService.countImagesBySku(sku);
                    if (existingCount > 0) {
                        batchResult.incrementSkipped();
                        batchResult.addSkippedSku(sku, "已有主图记录");
                        log.debug("SKU: {} 已有主图记录，跳过处理", sku);
                        continue;
                    }

                    // 查询商品信息
                    OmgProducts product = omgProductsService.selectOmgProductsBySku(sku);
                    if (product == null) {
                        batchResult.incrementFailed();
                        batchResult.addFailedSku(sku, "未找到商品信息");
                        continue;
                    }

                    // 获取平台信息
                    String mallType = getMallTypeByPlatform(product.getPlatform());
                    if (mallType == null) {
                        batchResult.incrementFailed();
                        batchResult.addFailedSku(sku, "不支持的平台: " + product.getPlatform());
                        continue;
                    }

                    // 处理图片
                    FindQcImageProcessResult result =
                            findQcImageProcessService.processProductImages(sku, mallType, "USD", "en");

                    if (result != null && result.isSuccess() &&
                        result.getPicListOssUrls() != null && !result.getPicListOssUrls().isEmpty()) {

                        // 更新商品主图
                        String mainImageUrl = result.getPicListOssUrls().get(0);
                        product.setMainImage(mainImageUrl);
                        omgProductsService.updateOmgProducts(product);

                        batchResult.incrementSuccess();
                        batchResult.addSuccessSku(sku, mainImageUrl);

                        log.debug("SKU: {} 主图更新成功", sku);
                    } else {
                        batchResult.incrementFailed();
                        batchResult.addFailedSku(sku, "图片处理失败");
                    }

                    // 添加延迟避免请求过于频繁
                    Thread.sleep(1000);

                } catch (Exception e) {
                    batchResult.incrementFailed();
                    batchResult.addFailedSku(sku, e.getMessage());
                    log.error("处理SKU异常: {}", sku, e);
                }
            }

            log.info("批量更新主图完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}",
                    batchResult.getTotal(), batchResult.getSuccess(),
                    batchResult.getFailed(), batchResult.getSkipped());

            return AjaxResult.success("批量更新完成", batchResult);

        } catch (Exception e) {
            log.error("批量更新主图表异常", e);
            return AjaxResult.error("批量更新异常: " + e.getMessage());
        }
    }


    /**
     * @Author: 系统
     * @Description: 获取omg商品表中的所有SKU并批量更新主图到OSS和数据库
     * @DateTime: 2025/7/19
     * @Return 批量更新结果
     */
    @Log(title = "批量更新所有商品主图", businessType = BusinessType.UPDATE)
    @PostMapping("/updateAllProductMainImages")
    public AjaxResult updateAllProductMainImages() {
        try {
            log.info("开始获取omg商品表中的所有SKU并批量更新主图");

            // 1. 获取所有商品的SKU和平台信息
            List<OmgProducts> allProducts = omgProductsService.selectAllProductsWithSkuAndPlatform();

            if (allProducts == null || allProducts.isEmpty()) {
                log.warn("omg商品表中没有找到任何商品");
                return AjaxResult.error("商品表中没有数据");
            }

            log.info("从omg商品表中获取到 {} 个商品", allProducts.size());

            // 2. 批量处理所有商品
            AllProductsUpdateResult result = processAllProducts(allProducts);

            log.info("批量更新所有商品主图完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}, 不支持: {}",
                    result.getTotal(), result.getSuccess(), result.getFailed(),
                    result.getSkipped(), result.getUnsupported());

            return AjaxResult.success("批量更新完成", result);

        } catch (Exception e) {
            log.error("批量更新所有商品主图异常", e);
            return AjaxResult.error("批量更新异常: " + e.getMessage());
        }
    }

    /**
     * @Author: 系统
     * @Description: 根据平台类型批量更新商品主图
     * @DateTime: 2025/1/19
     * @Params: platform 平台类型
     * @Return 批量更新结果
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:edit')")
    @Log(title = "按平台批量更新商品主图", businessType = BusinessType.UPDATE)
    @PostMapping("/updateProductMainImagesByPlatform")
    public AjaxResult updateProductMainImagesByPlatform(@RequestParam String platform) {
        try {
            log.info("开始按平台批量更新商品主图，平台: {}", platform);

            // 1. 根据平台获取商品列表
            List<OmgProducts> platformProducts = omgProductsService.selectProductsByPlatform(platform);

            if (platformProducts == null || platformProducts.isEmpty()) {
                log.warn("平台 {} 中没有找到任何商品", platform);
                return AjaxResult.error("该平台中没有商品数据");
            }

            log.info("从平台 {} 中获取到 {} 个商品", platform, platformProducts.size());

            // 2. 检查平台是否支持
            String mallType = getMallTypeByPlatform(platform);
            if (mallType == null) {
                log.warn("平台 {} 不支持FindQC处理", platform);
                return AjaxResult.error("不支持的平台类型: " + platform);
            }

            // 3. 批量处理该平台的商品
            AllProductsUpdateResult result = processAllProducts(platformProducts);

            log.info("平台 {} 批量更新完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}",
                    platform, result.getTotal(), result.getSuccess(), result.getFailed(), result.getSkipped());

            return AjaxResult.success("平台商品批量更新完成", result);

        } catch (Exception e) {
            log.error("按平台批量更新商品主图异常，平台: {}", platform, e);
            return AjaxResult.error("批量更新异常: " + e.getMessage());
        }
    }

    /**
     * @Author: 系统
     * @Description: 获取需要更新主图的商品数量统计
     * @DateTime: 2025/1/19
     * @Return 统计结果
     */
    @PreAuthorize("@ss.hasPermi('system:OmgProducts:list')")
    @GetMapping("/getMainImageUpdateStats")
    public AjaxResult getMainImageUpdateStats() {
        try {
            log.info("开始统计需要更新主图的商品数量");

            // 1. 获取所有商品
            List<OmgProducts> allProducts = omgProductsService.selectAllProductsWithSkuAndPlatform();

            MainImageUpdateStats stats = new MainImageUpdateStats();
            stats.setTotalProducts(allProducts.size());

            // 2. 统计各平台商品数量
            java.util.Map<String, Integer> platformStats = new java.util.HashMap<>();
            java.util.Map<String, Integer> needUpdateStats = new java.util.HashMap<>();

            for (OmgProducts product : allProducts) {
                String platform = product.getPlatform();

                // 统计平台商品数量
                platformStats.put(platform, platformStats.getOrDefault(platform, 0) + 1);

                // 检查是否需要更新（主图表中没有记录）
                int existingCount = omgProductMainImagesService.countImagesBySku(product.getSku());
                if (existingCount == 0) {
                    needUpdateStats.put(platform, needUpdateStats.getOrDefault(platform, 0) + 1);
                    stats.incrementNeedUpdate();
                } else {
                    stats.incrementAlreadyHave();
                }
            }

            stats.setPlatformStats(platformStats);
            stats.setNeedUpdateStats(needUpdateStats);

            log.info("统计完成，总商品数: {}, 需要更新: {}, 已有记录: {}",
                    stats.getTotalProducts(), stats.getNeedUpdate(), stats.getAlreadyHave());

            return AjaxResult.success("统计完成", stats);

        } catch (Exception e) {
            log.error("统计需要更新主图的商品数量异常", e);
            return AjaxResult.error("统计异常: " + e.getMessage());
        }
    }

    /**
     * 处理所有商品的主图更新
     */
    private AllProductsUpdateResult processAllProducts(List<OmgProducts> products) {
        AllProductsUpdateResult result = new AllProductsUpdateResult();
        result.setTotal(products.size());

        for (OmgProducts product : products) {
            try {
                String sku = product.getSku();
                String platform = product.getPlatform();

                // 检查SKU是否有效
                if (sku == null || sku.trim().isEmpty()) {
                    result.incrementFailed();
                    result.addFailedProduct(sku, "SKU为空");
                    continue;
                }

                // 检查是否已有记录
                int existingCount = omgProductMainImagesService.countImagesBySku(sku);
                if (existingCount > 0) {
                    result.incrementSkipped();
                    result.addSkippedProduct(sku, "已有主图记录");
                    log.debug("SKU: {} 已有主图记录，跳过处理", sku);
                    continue;
                }

                // 检查平台是否支持
                String mallType = getMallTypeByPlatform(platform);
                if (mallType == null) {
                    result.incrementUnsupported();
                    result.addUnsupportedProduct(sku, "不支持的平台: " + platform);
                    log.debug("SKU: {} 平台 {} 不支持，跳过处理", sku, platform);
                    continue;
                }

                // 处理图片
                FindQcImageProcessResult processResult =
                        findQcImageProcessService.processProductImages(sku, mallType, "USD", "en");

                if (processResult != null && processResult.isSuccess() &&
                    processResult.getPicListOssUrls() != null && !processResult.getPicListOssUrls().isEmpty()) {

                    // 更新商品主图
                    String mainImageUrl = processResult.getPicListOssUrls().get(0);
                    product.setMainImage(mainImageUrl);
                    omgProductsService.updateOmgProducts(product);

                    result.incrementSuccess();
                    result.addSuccessProduct(sku, mainImageUrl, processResult.getPicListOssUrls().size());

                    log.debug("SKU: {} 主图更新成功，图片数量: {}", sku, processResult.getPicListOssUrls().size());
                } else {
                    result.incrementFailed();
                    result.addFailedProduct(sku, "图片处理失败: " +
                            (processResult != null ? processResult.getMessage() : "未知错误"));
                }

                // 添加延迟避免请求过于频繁
                Thread.sleep(1500);

            } catch (Exception e) {
                result.incrementFailed();
                result.addFailedProduct(product.getSku(), "处理异常: " + e.getMessage());
                log.error("处理商品异常，SKU: {}", product.getSku(), e);
            }
        }

        return result;
    }

    @GetMapping("/getAll")
    public AjaxResult getAll(OmgProducts omgProducts) {

        startPage();
        List<OmgProducts> all = omgProductsService.getAll(omgProducts);
        PageInfo<OmgProducts> pageInfo = new PageInfo<>(all);
        HashMap<Object, Object>  map = new HashMap<>();
        map.put("list",pageInfo.getList());
        map.put("total",pageInfo.getTotal());
        map.put("pageNum",pageInfo.getPageNum());
        map.put("pageSize",pageInfo.getPageSize());
        return AjaxResult.success(map);
    }

}
