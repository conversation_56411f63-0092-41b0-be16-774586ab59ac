package com.ylkj.common.service;

import org.springframework.stereotype.Service;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.function.Function;

/**
 * 通用缓存服务
 * 提供线程安全的缓存操作
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class CacheService {
    
    // 多个缓存容器
    private final Map<String, Map<String, Object>> cacheContainers = new ConcurrentHashMap<>();
    
    /**
     * 获取或创建缓存容器
     * 
     * @param containerName 容器名称
     * @return 缓存容器
     */
    private Map<String, Object> getOrCreateContainer(String containerName) {
        return cacheContainers.computeIfAbsent(containerName, k -> new ConcurrentHashMap<>());
    }
    
    /**
     * 存储缓存值
     * 
     * @param containerName 容器名称
     * @param key 缓存键
     * @param value 缓存值
     * @param <T> 值类型
     */
    public <T> void put(String containerName, String key, T value) {
        if (key != null && value != null) {
            getOrCreateContainer(containerName).put(key, value);
        }
    }
    
    /**
     * 获取缓存值
     * 
     * @param containerName 容器名称
     * @param key 缓存键
     * @param valueType 值类型
     * @param <T> 值类型
     * @return 缓存值，如果不存在则返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String containerName, String key, Class<T> valueType) {
        Object value = getOrCreateContainer(containerName).get(key);
        if (value != null && valueType.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 获取缓存值，如果不存在则通过函数计算并缓存
     * 
     * @param containerName 容器名称
     * @param key 缓存键
     * @param valueType 值类型
     * @param supplier 值提供函数
     * @param <T> 值类型
     * @return 缓存值
     */
    public <T> T getOrCompute(String containerName, String key, Class<T> valueType, Function<String, T> supplier) {
        T value = get(containerName, key, valueType);
        if (value == null) {
            value = supplier.apply(key);
            if (value != null) {
                put(containerName, key, value);
            }
        }
        return value;
    }
    
    /**
     * 检查缓存是否存在
     * 
     * @param containerName 容器名称
     * @param key 缓存键
     * @return true-存在，false-不存在
     */
    public boolean containsKey(String containerName, String key) {
        return getOrCreateContainer(containerName).containsKey(key);
    }
    
    /**
     * 移除缓存值
     * 
     * @param containerName 容器名称
     * @param key 缓存键
     * @return 被移除的值
     */
    @SuppressWarnings("unchecked")
    public <T> T remove(String containerName, String key, Class<T> valueType) {
        Object value = getOrCreateContainer(containerName).remove(key);
        if (value != null && valueType.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 清空指定容器的所有缓存
     * 
     * @param containerName 容器名称
     */
    public void clear(String containerName) {
        Map<String, Object> container = cacheContainers.get(containerName);
        if (container != null) {
            container.clear();
        }
    }
    
    /**
     * 清空所有缓存
     */
    public void clearAll() {
        cacheContainers.clear();
    }
    
    /**
     * 获取容器大小
     * 
     * @param containerName 容器名称
     * @return 容器大小
     */
    public int size(String containerName) {
        Map<String, Object> container = cacheContainers.get(containerName);
        return container != null ? container.size() : 0;
    }
    
    /**
     * 获取容器中的所有键
     * 
     * @param containerName 容器名称
     * @return 键集合
     */
    public Set<String> keySet(String containerName) {
        Map<String, Object> container = cacheContainers.get(containerName);
        return container != null ? new HashSet<>(container.keySet()) : new HashSet<>();
    }
    
    /**
     * 批量存储
     * 
     * @param containerName 容器名称
     * @param data 数据映射
     */
    public void putAll(String containerName, Map<String, Object> data) {
        if (data != null && !data.isEmpty()) {
            getOrCreateContainer(containerName).putAll(data);
        }
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Integer> getStats() {
        Map<String, Integer> stats = new ConcurrentHashMap<>();
        for (Map.Entry<String, Map<String, Object>> entry : cacheContainers.entrySet()) {
            stats.put(entry.getKey(), entry.getValue().size());
        }
        return stats;
    }
    
    /**
     * 检查容器是否为空
     * 
     * @param containerName 容器名称
     * @return true-为空，false-不为空
     */
    public boolean isEmpty(String containerName) {
        Map<String, Object> container = cacheContainers.get(containerName);
        return container == null || container.isEmpty();
    }
    
    /**
     * 获取所有容器名称
     * 
     * @return 容器名称集合
     */
    public Set<String> getContainerNames() {
        return new HashSet<>(cacheContainers.keySet());
    }
    
    /**
     * 删除整个容器
     * 
     * @param containerName 容器名称
     * @return 是否删除成功
     */
    public boolean removeContainer(String containerName) {
        return cacheContainers.remove(containerName) != null;
    }
    
    // 常用的容器名称常量
    public static final String CATEGORY_CACHE = "category_cache";
    public static final String BRAND_CACHE = "brand_cache";
    public static final String SKU_CACHE = "sku_cache";
    public static final String USER_CACHE = "user_cache";
    public static final String PRODUCT_CACHE = "product_cache";
    public static final String IMPORT_CACHE = "import_cache";
}
