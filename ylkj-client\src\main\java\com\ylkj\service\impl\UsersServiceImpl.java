package com.ylkj.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ylkj.common.utils.SecurityUtils;
import com.ylkj.common.utils.StringUtils;
import com.ylkj.model.domain.Users;
import com.ylkj.mapper.UsersMapper;
import com.ylkj.model.vo.ChangePasswordVo;
import com.ylkj.model.vo.UsersVo;
import com.ylkj.service.IUsersService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
@Service
public class UsersServiceImpl extends ServiceImpl<UsersMapper, Users> implements IUsersService {


//    /**
//     * 用户登录
//     * @param username 用户名
//     * @param password 密码
//     * @return 用户信息
//     */
//    @Override
//    public Users login(String username, String password){
//        // 1. 参数基础校验
//        validateCredentials(username, password);
//
//        // 2. 密码规则校验
//        validatePasswordFormat(password);
//
//        // 3. 查询用户信息
//        Users user = findUserByUsername(username);
//
//        // 4. 密码匹配校验
//        verifyPasswordMatch(password, user.getPassword());
//
//        return user;
//    }

    /**
     * 用户邮箱登录
     * @param email 邮箱
     * @param password 密码
     * @return 用户信息
     */
    @Override
    public Users login(String email, String password){
        // 1. 参数基础校验
        if (StringUtils.isEmpty(email) || StringUtils.isEmpty(password)) {
            throw ExceptionUtil.wrapRuntime("邮箱或密码不能为空");
        }
        
        // 2. 密码规则校验
//        validatePasswordFormat(password);

        // 3. 查询用户信息
        Users user = baseMapper.selectUserByEmail(email);if (user == null) {
            throw ExceptionUtil.wrapRuntime("用户不存在");
        }

        // 4. 密码匹配校验
        if (!SecurityUtils.matchesPassword(password, user.getPassword())) {
            throw ExceptionUtil.wrapRuntime("密码不正确");
        }

        return user;
    }

    @Override
    public Long register(UsersVo usersVo) {

        if (usersVo.getUsername().isEmpty() || usersVo.getPassword().isEmpty()) {
            throw ExceptionUtil.wrapRuntime("用户名或密码不能为空");
        }
        if (baseMapper.selectByUsername(usersVo.getUsername()) != null) {
            throw ExceptionUtil.wrapRuntime("用户名已存在");
        }
        // 检查邮箱是否已存在
        if (StringUtils.isNotEmpty(usersVo.getEmail()) && baseMapper.selectUserByEmail(usersVo.getEmail()) != null) {
            throw ExceptionUtil.wrapRuntime("This email has already been registered");
        }
        // 密码加密
        usersVo.setPassword(SecurityUtils.encryptPassword(usersVo.getPassword()));

        System.err.println(usersVo.getPassword());

        int register = baseMapper.register(usersVo);

//        ClientUserPoints userPoints = new ClientUserPoints();
//        userPoints.setUserId(usersVo.getUserId());
//        userPoints.setTotalPoints(100);
//        userPoints.setAvailablePoints(100);
//        userPoints.setUsedPoints(0);
//        userPoints.setExpiredPoints(0);
//        userPoints.setLevel("Bronze");
//        userPoints.setCreatedAt(LocalDateTime.now());
//        //执行注册之后往积分表里面插入一条初始记录
//        clientUserPointsMapper.insertUserPoints(userPoints);
//        //插入积分历史记录表
//        ClientPointsHistory clientPointsHistory = new ClientPointsHistory();
//        clientPointsHistory.setUserId(usersVo.getUserId());
//        clientPointsHistory.setPoints(100);
//            clientPointsHistory.setType("earn");
//        clientPointsHistory.setAction("register");
//        clientPointsHistory.setDescription("Registration Reward");
//        clientPointsHistory.setCreatedAt(LocalDateTime.now());
//        clientPointsHistoryMapper.insertHistory(clientPointsHistory);
        if (register > 0) {
            // 注册成功，返回用户ID
            return usersVo.getUserId();
        }
        return null;
    }

    @Override
    public Users selectByEmail(String email) {
        return baseMapper.selectUserByEmail(email);
    }

    /**
     * 根据谷歌ID查询用户
     * @param googleId 谷歌账号ID
     * @return 用户信息
     */
    @Override
    public Users findByGoogleId(String googleId) {
        if (StringUtils.isEmpty(googleId)) {
            throw ExceptionUtil.wrapRuntime("谷歌ID不能为空");
        }
        
        LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Users::getGoogleId, googleId);
        
        return baseMapper.selectOne(queryWrapper);
    }

    /**
     * 基础参数校验
     */
    private void validateCredentials(String username, String password) {
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            throw ExceptionUtil.wrapRuntime("用户名或密码不能为空");
        }
        if (username.length() < 6 || username.length() > 20) {
            throw ExceptionUtil.wrapRuntime("用户名长度需在6-20位之间");
        }
        if (password.length() < 6 || password.length() > 20) {
            throw ExceptionUtil.wrapRuntime("密码长度需在6-20位之间");
        }
    }

    /**
     * 密码格式校验
     */
    private void validatePasswordFormat(String password) {
        int complexityFlags = 0;

        // 包含字母
        if (password.matches(".*[a-zA-Z].*")) complexityFlags++;
        // 包含数字
        if (password.matches(".*\\d.*")) complexityFlags++;
        // 包含特殊符号
        if (password.matches(".*[!@#$%^&*()_+].*")) complexityFlags++;

        if (complexityFlags < 2) {
            throw ExceptionUtil.wrapRuntime("密码需包含至少两种字符类型（字母、数字、特殊符号）");
        }
    }

    /**
     * 查询用户信息
     */
    private Users findUserByUsername(String username) {
        Users user = baseMapper.selectByUsername(username);
        if (user == null) {
            throw ExceptionUtil.wrapRuntime("用户不存在");
        }
        return user;
    }

    /**
     * 密码匹配校验（使用安全工具类）
     */
    private void verifyPasswordMatch(String inputPassword, String storedHash) {
        SecurityUtils.matchesPassword(inputPassword, storedHash);
    }



    /**
     * 小林
     * 根据用户邮箱去修改用户个人信息
     * @param usersVo
     * @return
     */
    @Override
    public int ChangeUserInformation(UsersVo usersVo) {
        int users =  baseMapper.ChangeUserInformation(usersVo);
        if(users != 0){
            return users;
        }
        return 0;
    }

    /**
     * 小林
     * 修改用户密码
     * @param changePasswordVo
     * @return
     */
    @Override
    public int changePassword(ChangePasswordVo changePasswordVo) {

        // 1. 参数基础校验
        if (StringUtils.isEmpty(changePasswordVo.getEmail()) || StringUtils.isEmpty(changePasswordVo.getCurrentPassword()) || StringUtils.isEmpty(changePasswordVo.getNewPassword())) {
            throw ExceptionUtil.wrapRuntime("邮箱、当前密码和新密码不能为空");
        }

        // 2. 查询用户信息
        Users user = baseMapper.selectUserByEmail(changePasswordVo.getEmail());
        if (user == null) {
            throw ExceptionUtil.wrapRuntime("用户不存在");
        }


        // 3. 验证当前密码是否匹配
        if (!SecurityUtils.matchesPassword(changePasswordVo.getCurrentPassword(), user.getPassword())) {
            throw ExceptionUtil.wrapRuntime("当前密码不正确");
        }


        // 4. 验证新密码格式
        validatePasswordFormat(changePasswordVo.getNewPassword());

        // 5. 加密新密码
        String encryptedPassword = SecurityUtils.encryptPassword(changePasswordVo.getNewPassword());

        // 6. 更新数据库中的密码
        ChangePasswordVo changePasswordVo1 = new ChangePasswordVo();
        changePasswordVo1.setEmail(changePasswordVo.getEmail());
        changePasswordVo1.setNewPassword(encryptedPassword);

        return baseMapper.updatePasswordByEmail(changePasswordVo1);
    }


    /**
     * 更新用户最后登陆时间
     * @param users
     * @return
     */
    @Override
    public int updateLastLoginAtInt(Users users) {
        int i = baseMapper.updateLastLoginAtInt(users);
        if(i > 0){
            return 1;
        }
        return 0;
    }

}
