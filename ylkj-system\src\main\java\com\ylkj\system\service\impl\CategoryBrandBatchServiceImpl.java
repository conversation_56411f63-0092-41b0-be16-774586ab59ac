package com.ylkj.system.service.impl;

import com.ylkj.system.mapper.OmgBrandsMapper;
import com.ylkj.system.mapper.SOmgCategoriesMapper;
import com.ylkj.system.model.domain.OmgBrands;
import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.model.domain.SOmgCategories;
import com.ylkj.system.model.dto.BrandNameIdMapping;
import com.ylkj.system.model.dto.BrandQueryRequest;
import com.ylkj.system.model.dto.CategoryNameIdMapping;
import com.ylkj.system.service.CategoryBrandBatchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分类和品牌批量处理服务实现类
 * 提供高性能的批量分类和品牌处理功能
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class CategoryBrandBatchServiceImpl implements CategoryBrandBatchService {
    
    private static final Logger log = LoggerFactory.getLogger(CategoryBrandBatchServiceImpl.class);
    
    @Autowired
    private SOmgCategoriesMapper sOmgCategoriesMapper;
    
    @Autowired
    private OmgBrandsMapper omgBrandsMapper;
    
    // 性能统计
    private long totalProcessTime = 0;
    private int totalCategoriesProcessed = 0;
    private int totalBrandsProcessed = 0;
    private int batchCount = 0;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processCategoriesAndBrandsBatch(List<OmgProducts> products) {
        if (products == null || products.isEmpty()) {
            log.warn("商品列表为空，跳过批量处理");
            return;
        }
        
        long startTime = System.currentTimeMillis();
        batchCount++;
        
        log.info("开始批量处理分类和品牌，商品数量: {}", products.size());

        // 调试：打印前几个商品的分类信息
        System.out.println("=== 商品分类信息调试 ===");
        for (int i = 0; i < Math.min(5, products.size()); i++) {
            OmgProducts product = products.get(i);
            System.out.println("商品" + (i+1) + ": " + product.getName());
            System.out.println("  - categoryName: '" + product.getCategoryName() + "'");
            System.out.println("  - secondCategoryName: '" + product.getSecondCategoryName() + "'");
            System.out.println("  - categoryId: " + product.getCategoryId());
            System.out.println("  - brandId: " + product.getBrandId());
        }

        try {
            // 1. 收集所有唯一的分类名称
            Set<String> uniqueCategoryNames = products.stream()
                    .map(OmgProducts::getCategoryName)
                    .filter(Objects::nonNull)
                    .filter(name -> !name.trim().isEmpty())
                    .collect(Collectors.toSet());

            System.out.println("=== 收集到的分类信息 ===");
            System.out.println("唯一分类数量: " + uniqueCategoryNames.size());
            System.out.println("分类名称: " + uniqueCategoryNames);

            // 2. 批量处理分类
            Map<String, Long> categoryMapping = new HashMap<>();
            if (!uniqueCategoryNames.isEmpty()) {
                log.info("处理分类数量: {}", uniqueCategoryNames.size());
                categoryMapping = batchGetOrCreateCategoryIds(new ArrayList<>(uniqueCategoryNames));
                totalCategoriesProcessed += uniqueCategoryNames.size();
            } else {
                System.out.println("警告: 没有找到任何分类信息！");
            }
            
            // 3. 为商品设置分类ID
            System.out.println("=== 为商品设置分类ID ===");
            for (OmgProducts product : products) {
                if (product.getCategoryName() != null && !product.getCategoryName().trim().isEmpty()) {
                    Long categoryId = categoryMapping.get(product.getCategoryName());
                    product.setCategoryId(categoryId);
                    System.out.println("商品: " + product.getName() + ", 分类: " + product.getCategoryName() + " -> ID: " + categoryId);
                }
            }

            // 4. 收集所有唯一的品牌请求（品牌名称+分类ID）
            List<BrandQueryRequest> uniqueBrandRequests = products.stream()
                    .filter(p -> p.getSecondCategoryName() != null && !p.getSecondCategoryName().trim().isEmpty())
                    .filter(p -> p.getCategoryId() != null)
                    .map(p -> new BrandQueryRequest(p.getSecondCategoryName(), p.getCategoryId()))
                    .distinct()
                    .collect(Collectors.toList());

            System.out.println("=== 收集到的品牌信息 ===");
            System.out.println("唯一品牌数量: " + uniqueBrandRequests.size());
            System.out.println("品牌请求: " + uniqueBrandRequests);
            
            // 5. 批量处理品牌
            Map<String, Long> brandMapping = new HashMap<>();
            if (!uniqueBrandRequests.isEmpty()) {
                log.debug("处理品牌数量: {}", uniqueBrandRequests.size());
                brandMapping = batchGetOrCreateBrandIds(uniqueBrandRequests);
                totalBrandsProcessed += uniqueBrandRequests.size();
            }
            
            // 6. 为商品设置品牌ID和处理价格
            for (OmgProducts product : products) {
                // 设置品牌ID
                if (product.getSecondCategoryName() != null && !product.getSecondCategoryName().trim().isEmpty() 
                    && product.getCategoryId() != null) {
                    String brandKey = product.getSecondCategoryName() + "_" + product.getCategoryId();
                    Long brandId = brandMapping.get(brandKey);
                    product.setBrandId(brandId);
                }
                
                // 处理价格
                if (product.getPrice() != null) {
                    product.setPrice(product.getPrice().divide(new BigDecimal("1"), 2, RoundingMode.HALF_UP));
                }
                if (product.getOriginalPrice() != null) {
                    product.setOriginalPrice(product.getOriginalPrice().divide(new BigDecimal("1"), 2, RoundingMode.HALF_UP));
                }
            }
            
            long endTime = System.currentTimeMillis();
            totalProcessTime += (endTime - startTime);
            
            log.info("批量处理完成，耗时: {}ms, 处理分类: {}, 处理品牌: {}", 
                    (endTime - startTime), uniqueCategoryNames.size(), uniqueBrandRequests.size());
            
        } catch (Exception e) {
            log.error("批量处理分类和品牌失败", e);
            throw e;
        }
    }
    
    @Override
    public Map<String, Long> batchGetOrCreateCategoryIds(List<String> categoryNames) {
        return batchProcessCategoriesWithInsertIgnore(categoryNames);
    }

    @Override
    public Map<String, Long> batchGetOrCreateBrandIds(List<BrandQueryRequest> brandRequests) {
        return batchProcessBrandsWithInsertIgnore(brandRequests);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = org.springframework.transaction.annotation.Propagation.REQUIRES_NEW)
    public Map<String, Long> batchProcessCategoriesWithInsertIgnore(List<String> categoryNames) {
        if (categoryNames == null || categoryNames.isEmpty()) {
            return new HashMap<>();
        }

        try {
            log.info("开始批量处理分类，数量: {}", categoryNames.size());
            System.out.println("开始批量处理分类，数量: " + categoryNames.size());
            System.out.println("分类名称列表: " + categoryNames);

            // 1. 先批量插入（忽略重复）
            List<SOmgCategories> categoriesToInsert = categoryNames.stream()
                    .map(name -> {
                        SOmgCategories category = new SOmgCategories();
                        category.setName(name);
                        return category;
                    })
                    .collect(Collectors.toList());

            System.out.println("准备插入的分类对象数量: " + categoriesToInsert.size());
            int insertCount = sOmgCategoriesMapper.batchInsertCategoriesIgnore(categoriesToInsert);
            log.info("批量插入分类完成，新增: {} 个", insertCount);
            System.out.println("批量插入分类完成，新增: " + insertCount + " 个");

            // 2. 批量查询所有分类ID
            List<CategoryNameIdMapping> mappings = sOmgCategoriesMapper.batchSelectCategoryIdsByNames(categoryNames);
            System.out.println("查询到的分类映射数量: " + mappings.size());
            System.out.println("分类映射详情: " + mappings);

            // 3. 转换为Map
            Map<String, Long> result = mappings.stream()
                    .collect(Collectors.toMap(
                            CategoryNameIdMapping::getCategoryName,
                            CategoryNameIdMapping::getCategoryId,
                            (existing, replacement) -> existing
                    ));

            log.info("批量查询分类ID完成，获取到: {} 个映射", result.size());
            System.out.println("最终分类映射结果: " + result);
            return result;

        } catch (Exception e) {
            log.error("批量处理分类失败，错误详情: {}", e.getMessage(), e);
            System.err.println("批量处理分类失败，错误详情: " + e.getMessage());
            e.printStackTrace();
            // 回退到原有逐个处理方式
            System.out.println("回退到逐个处理分类...");
            return fallbackProcessCategories(categoryNames);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = org.springframework.transaction.annotation.Propagation.REQUIRES_NEW)
    public Map<String, Long> batchProcessBrandsWithInsertIgnore(List<BrandQueryRequest> brandRequests) {
        if (brandRequests == null || brandRequests.isEmpty()) {
            return new HashMap<>();
        }

        try {
            log.info("开始批量处理品牌，数量: {}", brandRequests.size());
            System.out.println("开始批量处理品牌，数量: " + brandRequests.size());
            System.out.println("品牌请求列表: " + brandRequests);

            // 1. 先批量插入（忽略重复）
            List<OmgBrands> brandsToInsert = brandRequests.stream()
                    .map(request -> {
                        OmgBrands brand = new OmgBrands();
                        brand.setName(request.getBrandName());
                        brand.setParentCategoryId(request.getCategoryId());
                        return brand;
                    })
                    .collect(Collectors.toList());

            System.out.println("准备插入的品牌对象数量: " + brandsToInsert.size());
            int insertCount = omgBrandsMapper.batchInsertBrandsIgnore(brandsToInsert);
            log.info("批量插入品牌完成，新增: {} 个", insertCount);
            System.out.println("批量插入品牌完成，新增: " + insertCount + " 个");

            // 2. 批量查询所有品牌ID
            List<BrandNameIdMapping> mappings = omgBrandsMapper.batchSelectBrandIdsByNames(brandRequests);
            System.out.println("查询到的品牌映射数量: " + mappings.size());
            System.out.println("品牌映射详情: " + mappings);

            // 3. 转换为Map（使用品牌键：品牌名称_分类ID）
            Map<String, Long> result = mappings.stream()
                    .collect(Collectors.toMap(
                            BrandNameIdMapping::getBrandKey,
                            BrandNameIdMapping::getBrandId,
                            (existing, replacement) -> existing
                    ));

            log.info("批量查询品牌ID完成，获取到: {} 个映射", result.size());
            System.out.println("最终品牌映射结果: " + result);
            return result;

        } catch (Exception e) {
            log.error("批量处理品牌失败，错误详情: {}", e.getMessage(), e);
            System.err.println("批量处理品牌失败，错误详情: " + e.getMessage());
            e.printStackTrace();
            // 回退到原有逐个处理方式
            System.out.println("回退到逐个处理品牌...");
            return fallbackProcessBrands(brandRequests);
        }
    }

    @Override
    public String getPerformanceStats() {
        if (batchCount == 0) {
            return "暂无批量处理统计信息";
        }

        double avgProcessTime = (double) totalProcessTime / batchCount;
        return String.format("批量处理统计 - 总批次: %d, 总耗时: %dms, 平均耗时: %.2fms, " +
                "处理分类总数: %d, 处理品牌总数: %d",
                batchCount, totalProcessTime, avgProcessTime,
                totalCategoriesProcessed, totalBrandsProcessed);
    }

    @Override
    public void clearCacheAndStats() {
        totalProcessTime = 0;
        totalCategoriesProcessed = 0;
        totalBrandsProcessed = 0;
        batchCount = 0;
        log.info("已清理缓存和统计信息");
    }

    /**
     * 回退方案：逐个处理分类（兼容原有逻辑）
     * 当批量处理失败时使用
     */
    private Map<String, Long> fallbackProcessCategories(List<String> categoryNames) {
        log.warn("使用回退方案逐个处理分类");
        Map<String, Long> categoryMapping = new HashMap<>();

        for (String categoryName : categoryNames) {
            try {
                Long categoryId = sOmgCategoriesMapper.selectCategoryIdByName(categoryName);
                if (categoryId == null) {
                    SOmgCategories category = new SOmgCategories();
                    category.setName(categoryName);
                    sOmgCategoriesMapper.insertOmgCategories(category);
                    categoryId = category.getCategoryId();
                }
                categoryMapping.put(categoryName, categoryId);
            } catch (Exception e) {
                log.error("处理分类失败: {}", categoryName, e);
            }
        }

        return categoryMapping;
    }

    /**
     * 回退方案：逐个处理品牌（兼容原有逻辑）
     * 当批量处理失败时使用
     */
    private Map<String, Long> fallbackProcessBrands(List<BrandQueryRequest> brandRequests) {
        log.warn("使用回退方案逐个处理品牌");
        Map<String, Long> brandMapping = new HashMap<>();

        for (BrandQueryRequest request : brandRequests) {
            try {
                Long brandId = omgBrandsMapper.selectBrandIdByName(request.getBrandName(), request.getCategoryId());
                if (brandId == null) {
                    OmgBrands brand = new OmgBrands();
                    brand.setName(request.getBrandName());
                    brand.setParentCategoryId(request.getCategoryId());
                    omgBrandsMapper.insertOmgBrands(brand);
                    brandId = brand.getBrandId();
                }
                String brandKey = request.getBrandName() + "_" + request.getCategoryId();
                brandMapping.put(brandKey, brandId);
            } catch (Exception e) {
                log.error("处理品牌失败: {}, 分类ID: {}", request.getBrandName(), request.getCategoryId(), e);
            }
        }

        return brandMapping;
    }

    /**
     * 测试方法：验证批量处理是否正常工作
     */
    public void testBatchProcessing() {
        System.out.println("=== 开始测试批量处理功能 ===");

        // 首先测试基本的Mapper功能
        try {
            System.out.println("测试基本Mapper功能...");
            Long existingCategoryId = sOmgCategoriesMapper.selectCategoryIdByName("不存在的分类");
            System.out.println("查询不存在分类的结果: " + existingCategoryId);
        } catch (Exception e) {
            System.err.println("基本Mapper测试失败: " + e.getMessage());
            e.printStackTrace();
            return;
        }

        // 测试分类处理
        List<String> testCategories = Arrays.asList("测试分类1", "测试分类2", "测试分类3");
        System.out.println("测试分类列表: " + testCategories);

        try {
            Map<String, Long> categoryResult = batchProcessCategoriesWithInsertIgnore(testCategories);
            System.out.println("分类处理结果: " + categoryResult);
        } catch (Exception e) {
            System.err.println("分类处理失败: " + e.getMessage());
            e.printStackTrace();
        }

        // 测试品牌处理
        List<BrandQueryRequest> testBrands = Arrays.asList(
            new BrandQueryRequest("测试品牌1", 1L),
            new BrandQueryRequest("测试品牌2", 1L)
        );
        System.out.println("测试品牌列表: " + testBrands);

        try {
            Map<String, Long> brandResult = batchProcessBrandsWithInsertIgnore(testBrands);
            System.out.println("品牌处理结果: " + brandResult);
        } catch (Exception e) {
            System.err.println("品牌处理失败: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("=== 测试完成 ===");
    }

    /**
     * 简化测试：直接测试单个分类插入
     */
    public void testSimpleInsert() {
        System.out.println("=== 开始简化测试 ===");

        try {
            // 测试单个分类插入
            SOmgCategories testCategory = new SOmgCategories();
            testCategory.setName("简化测试分类_" + System.currentTimeMillis());

            System.out.println("准备插入分类: " + testCategory.getName());
            int result = sOmgCategoriesMapper.insertOmgCategories(testCategory);
            System.out.println("插入结果: " + result + ", 生成的ID: " + testCategory.getCategoryId());

            // 测试查询
            Long queryResult = sOmgCategoriesMapper.selectCategoryIdByName(testCategory.getName());
            System.out.println("查询结果: " + queryResult);

        } catch (Exception e) {
            System.err.println("简化测试失败: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("=== 简化测试完成 ===");
    }
}
