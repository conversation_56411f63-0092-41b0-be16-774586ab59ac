package com.ylkj.system.service.impl;

import java.util.*;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import com.ylkj.common.utils.StringUtils;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylkj.common.utils.DateUtils;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ylkj.system.mapper.AgtPostsMapper;
import com.ylkj.system.mapper.AgtTagsMapper;
import com.ylkj.system.mapper.AgtPostTagsMapper;
import com.ylkj.system.mapper.AgtPostImagesMapper;
import com.ylkj.system.mapper.AgtPostCommentsMapper;
import com.ylkj.system.model.domain.AgtPosts;
import com.ylkj.system.model.domain.AgtTags;
import com.ylkj.system.model.domain.AgtPostTags;
import com.ylkj.system.model.domain.AgtPostImages;
import com.ylkj.system.model.domain.AgtPostComments;
import com.ylkj.system.service.IAgtPostsService;
import com.ylkj.system.model.dto.agtPosts.AgtPostsQuery;
import com.ylkj.system.model.vo.agtPosts.AgtPostsVo;
import com.ylkj.system.model.vo.agtPosts.AgtPostDetailVO;
import com.ylkj.system.model.dto.agtPosts.AgtPostDetailEdit;
import com.github.pagehelper.PageHelper;

/**
 * 帖子管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
public class AgtPostsServiceImpl extends ServiceImpl<AgtPostsMapper, AgtPosts> implements IAgtPostsService
{
    private static final Logger logger = LoggerFactory.getLogger(AgtPostsServiceImpl.class);
    
    @Resource
    private AgtPostsMapper agtPostsMapper;
    
    @Autowired(required = false)
    private AgtTagsMapper agtTagsMapper;
    
    @Autowired(required = false)
    private AgtPostTagsMapper agtPostTagsMapper;
    
    @Autowired(required = false)
    private AgtPostImagesMapper agtPostImagesMapper;
    
    @Autowired(required = false)
    private AgtPostCommentsMapper agtPostCommentsMapper;

    //region mybatis代码
    /**
     * 查询帖子管理
     * 
     * @param id 帖子管理主键
     * @return 帖子管理
     */
    @Override
    public AgtPosts selectAgtPostsById(Long id)
    {
        return agtPostsMapper.selectAgtPostsById(id);
    }

    /**
     * 查询帖子管理列表
     * 
     * @param agtPosts 帖子管理
     * @return 帖子管理
     */
    @Override
    public List<AgtPosts> selectAgtPostsList(AgtPosts agtPosts)
    {
        return agtPostsMapper.selectAgtPostsList(agtPosts);
    }

    /**
     * 新增帖子管理
     * 
     * @param agtPosts 帖子管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertAgtPosts(AgtPosts agtPosts) {
        logger.info("新增帖子 - 标题: {}", agtPosts.getTitle());
        
        try {
            // 1. 设置创建时间
            agtPosts.setCreateTime(DateUtils.getNowDate());
            
            // 2. 插入帖子基本信息
            int result = agtPostsMapper.insertAgtPosts(agtPosts);
            
            if (result > 0) {
                Long postId = agtPosts.getId();
                logger.info("帖子插入成功 - 帖子ID: {}", postId);
                
                // 3. 处理标签关联 - 检查方法是否存在
                try {
                    List<String> tags = agtPosts.getTags();
                    if (tags != null && !tags.isEmpty()) {
                        insertPostTags(postId, tags);
                    }
                } catch (Exception e) {
                    logger.warn("处理标签时出错，可能实体类未定义getTags方法: {}", e.getMessage());
                }
                
                // 4. 处理图片关联 - 检查方法是否存在
                try {
                    String[] imageUrls = agtPosts.getImageUrls();
                    if (imageUrls != null && imageUrls.length > 0) {
                        insertPostImages(postId, imageUrls);
                    }
                } catch (Exception e) {
                    logger.warn("处理图片时出错，可能实体类未定义getImageUrls方法: {}", e.getMessage());
                }
                
                logger.info("帖子新增完成 - 帖子ID: {}", postId);
            }
            
            return result;
        } catch (Exception e) {
            logger.error("新增帖子异常 - 标题: {}", agtPosts.getTitle(), e);
            throw e;
        }
    }

    /**
     * 修改帖子管理
     * 
     * @param agtPosts 帖子管理
     * @return 结果
     */
    @Override
    public int updateAgtPosts(AgtPosts agtPosts)
    {
        agtPosts.setUpdateTime(DateUtils.getNowDate());
        return agtPostsMapper.updateAgtPosts(agtPosts);
    }

    /**
     * 批量删除帖子管理
     * 
     * @param ids 需要删除的帖子管理主键
     * @return 结果
     */
    @Override
    public int deleteAgtPostsByIds(Long[] ids)
    {
        return agtPostsMapper.deleteAgtPostsByIds(ids);
    }

    /**
     * 删除帖子管理信息
     * 
     * @param id 帖子管理主键
     * @return 结果
     */
    @Override
    public int deleteAgtPostsById(Long id)
    {
        return agtPostsMapper.deleteAgtPostsById(id);
    }

    /**
     * 获取所有帖子（不分页）
     * 
     * @param agtPosts 查询条件
     * @return 帖子列表
     */
    @Override
    public List<AgtPosts> selectAllAgtPosts(AgtPosts agtPosts) {
        // 使用原生MyBatis查询，避开PageHelper的分页限制
        try {
            // 记录当前状态
            PageHelper.clearPage();
            // 执行查询
            return agtPostsMapper.selectAgtPostsList(agtPosts);
        } finally {
            // 确保清理线程变量，避免影响其他查询
            PageHelper.clearPage();
        }
    }
    //endregion
    @Override
    public QueryWrapper<AgtPosts> getQueryWrapper(AgtPostsQuery agtPostsQuery){
        QueryWrapper<AgtPosts> queryWrapper = new QueryWrapper<>();
        //如果不使用params可以删除
        Map<String, Object> params = agtPostsQuery.getParams();
        if (StringUtils.isNull(params)) {
            params = new HashMap<>();
        }
        String title = agtPostsQuery.getTitle();
        queryWrapper.eq(StringUtils.isNotEmpty(title) ,"title",title);

        String content = agtPostsQuery.getContent();
        queryWrapper.eq(StringUtils.isNotEmpty(content) ,"content",content);

        Long userId = agtPostsQuery.getUserId();
        queryWrapper.eq( StringUtils.isNotNull(userId),"user_id",userId);

        String image = agtPostsQuery.getImage();
        queryWrapper.eq(StringUtils.isNotEmpty(image) ,"image",image);

        return queryWrapper;
    }

    @Override
    public List<AgtPostsVo> convertVoList(List<AgtPosts> agtPostsList) {
        if (StringUtils.isEmpty(agtPostsList)) {
            return Collections.emptyList();
        }
        return agtPostsList.stream().map(AgtPostsVo::objToVo).collect(Collectors.toList());
    }
    
    /**
     * 获取帖子详情列表，包括标签、图片、评论等
     *
     * @param agtPostsQuery 查询条件
     * @return 帖子详情VO列表
     */
    @Override
    public List<AgtPostDetailVO> getPostDetailList(AgtPostsQuery agtPostsQuery) {
        logger.info("获取帖子详情列表 - 开始查询");
        
        try {
            // 1. 获取帖子基本信息列表
            AgtPosts agtPosts = AgtPostsQuery.queryToObj(agtPostsQuery);
            List<AgtPosts> postsList = agtPostsMapper.selectAgtPostsList(agtPosts);
            
            if (postsList == null || postsList.isEmpty()) {
                logger.info("获取帖子详情列表 - 未找到符合条件的帖子");
                return Collections.emptyList();
            }
            
            // 2. 转换为详情VO列表
            List<AgtPostDetailVO> detailVOList = new ArrayList<>(postsList.size());
            for (AgtPosts post : postsList) {
                // 获取每个帖子的详情
                AgtPostDetailVO detailVO = getPostDetailById(post.getId());
                if (detailVO != null) {
                    detailVOList.add(detailVO);
                }
            }
            
            logger.info("获取帖子详情列表成功 - 共{}条记录", detailVOList.size());
            return detailVOList;
        } catch (Exception e) {
            logger.error("获取帖子详情列表异常", e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 获取帖子详情，包括标签、图片、评论等
     *
     * @param id 帖子ID
     * @return 帖子详情VO
     */
    @Override
    public AgtPostDetailVO getPostDetailById(Long id) {
        logger.info("获取帖子详情 - 帖子ID: {}", id);
        
        try {
            // 1. 获取帖子基本信息
            AgtPosts post = agtPostsMapper.selectAgtPostsById(id);
            if (post == null) {
                logger.warn("帖子不存在 - ID: {}", id);
                return null;
            }
            
            // 2. 创建返回对象并设置基本信息
            AgtPostDetailVO detailVO = new AgtPostDetailVO();
            BeanUtils.copyProperties(post, detailVO);
            
            // 3. 获取用户信息（可以从系统用户表获取）
            // TODO: 从系统用户表获取用户信息
            
            // 4. 获取标签信息
            List<AgtPostDetailVO.AgtTagVO> tags = getPostTags(id);
            detailVO.setTags(tags);
            
            // 5. 获取图片信息
            List<String> imageUrls = getPostImages(id);
            detailVO.setImageUrls(imageUrls);
            
            // 6. 获取评论信息
            List<AgtPostDetailVO.AgtCommentVO> comments = getPostComments(id);
            detailVO.setComments(comments);
            detailVO.setCommentCount(comments != null ? comments.size() : 0);
            
            logger.info("获取帖子详情成功 - 帖子ID: {}", id);
            return detailVO;
        } catch (Exception e) {
            logger.error("获取帖子详情异常 - 帖子ID: {}", id, e);
            return null;
        }
    }
    
    /**
     * 更新帖子详情，包括标签、图片、评论等
     *
     * @param postDetailEdit 帖子详情编辑对象
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePostDetail(AgtPostDetailEdit postDetailEdit) {
        logger.info("更新帖子详情 - 帖子ID: {}", postDetailEdit.getId());
        
        try {
            // 1. 更新帖子基本信息
            AgtPosts post = new AgtPosts();
            BeanUtils.copyProperties(postDetailEdit, post);
            post.setUpdateTime(DateUtils.getNowDate());
            int result = agtPostsMapper.updateAgtPosts(post);
            
            if (result <= 0) {
                logger.warn("更新帖子基本信息失败 - 帖子ID: {}", postDetailEdit.getId());
                return false;
            }
            
            // 2. 更新标签关联
            if (postDetailEdit.getTagIds() != null || postDetailEdit.getTagNames() != null) {
                updatePostTags(postDetailEdit);
            }
            
            // 3. 更新图片关联
            if (postDetailEdit.getImageUrls() != null) {
                updatePostImages(postDetailEdit);
            }
            
            // 4. 处理评论删除
            if (postDetailEdit.getCommentIdsToDelete() != null && !postDetailEdit.getCommentIdsToDelete().isEmpty()) {
                deletePostCommentBatch(postDetailEdit.getCommentIdsToDelete().toArray(new Long[0]));
            }
            
            logger.info("更新帖子详情成功 - 帖子ID: {}", postDetailEdit.getId());
            return true;
        } catch (Exception e) {
            logger.error("更新帖子详情异常 - 帖子ID: {}", postDetailEdit.getId(), e);
            throw e; // 抛出异常以触发事务回滚
        }
    }
    
    /**
     * 删除帖子评论
     *
     * @param commentId 评论ID
     * @return 是否成功
     */
    @Override
    public boolean deletePostComment(Long commentId) {
        logger.info("删除帖子评论 - 评论ID: {}", commentId);
        
        try {
            // TODO: 调用评论服务删除评论
            // 示例代码，需要根据实际情况调整
            // return postCommentsService.deleteById(commentId) > 0;
            return true;
        } catch (Exception e) {
            logger.error("删除帖子评论异常 - 评论ID: {}", commentId, e);
            return false;
        }
    }
    
    /**
     * 批量删除帖子评论
     *
     * @param commentIds 评论ID数组
     * @return 是否成功
     */
    @Override
    public boolean deletePostCommentBatch(Long[] commentIds) {
        logger.info("批量删除帖子评论 - 评论数量: {}", commentIds.length);
        
        try {
            // TODO: 调用评论服务批量删除评论
            // 示例代码，需要根据实际情况调整
            // return postCommentsService.deleteBatchIds(Arrays.asList(commentIds)) > 0;
            return true;
        } catch (Exception e) {
            logger.error("批量删除帖子评论异常", e);
            return false;
        }
    }
    
    /**
     * 获取帖子标签
     *
     * @param postId 帖子ID
     * @return 标签列表
     */
    private List<AgtPostDetailVO.AgtTagVO> getPostTags(Long postId) {
        logger.info("获取帖子标签 - 帖子ID: {}", postId);
        List<AgtPostDetailVO.AgtTagVO> tagVOList = new ArrayList<>();
        
        try {
            // 1. 通过帖子ID从关联表查询标签ID列表
            if (agtPostTagsMapper != null) {
                LambdaQueryWrapper<AgtPostTags> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(AgtPostTags::getPostId, postId);
                List<AgtPostTags> postTagsList = agtPostTagsMapper.selectList(queryWrapper);
                
                if (postTagsList != null && !postTagsList.isEmpty()) {
                    // 2. 提取标签ID列表
                    List<Long> tagIds = postTagsList.stream()
                            .map(AgtPostTags::getTagId)
                            .collect(Collectors.toList());
                    
                    // 3. 根据标签ID列表查询标签详情
                    if (agtTagsMapper != null && !tagIds.isEmpty()) {
                        LambdaQueryWrapper<AgtTags> tagsQueryWrapper = new LambdaQueryWrapper<>();
                        tagsQueryWrapper.in(AgtTags::getId, tagIds);
                        List<AgtTags> tagsList = agtTagsMapper.selectList(tagsQueryWrapper);
                        
                        // 4. 转换为VO对象
                        for (AgtTags tag : tagsList) {
                            AgtPostDetailVO.AgtTagVO tagVO = new AgtPostDetailVO.AgtTagVO();
                            tagVO.setId(tag.getId());
                            tagVO.setName(tag.getName());
                            tagVOList.add(tagVO);
                        }
                    }
                }
            } else {
                // 如果Mapper未注入，则尝试从客户端获取数据
                logger.info("AgtPostTagsMapper未注入，尝试从客户端获取标签数据");
                // 这里可以调用客户端的标签服务，但为避免冲突，我们先使用空实现
            }
            
            logger.info("获取帖子标签成功 - 帖子ID: {}, 标签数量: {}", postId, tagVOList.size());
            return tagVOList;
        } catch (Exception e) {
            logger.error("获取帖子标签异常 - 帖子ID: {}", postId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取帖子图片
     *
     * @param postId 帖子ID
     * @return 图片URL列表
     */
    private List<String> getPostImages(Long postId) {
        logger.info("获取帖子图片 - 帖子ID: {}", postId);
        List<String> imageUrls = new ArrayList<>();
        
        try {
            // 从帖子图片表获取图片URL列表
            if (agtPostImagesMapper != null) {
                LambdaQueryWrapper<AgtPostImages> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(AgtPostImages::getPostId, postId);
                List<AgtPostImages> postImagesList = agtPostImagesMapper.selectList(queryWrapper);
                
                if (postImagesList != null && !postImagesList.isEmpty()) {
                    imageUrls = postImagesList.stream()
                            .map(AgtPostImages::getImageUrl)
                            .filter(StringUtils::isNotEmpty)
                            .collect(Collectors.toList());
                }
            } else {
                // 如果Mapper未注入，则尝试从客户端获取数据
                logger.info("AgtPostImagesMapper未注入，尝试从客户端获取图片数据");
                // 这里可以调用客户端的图片服务，但为避免冲突，我们先使用空实现
            }
            
            // 如果图片列表为空，但帖子本身有image字段，则添加该图片
            AgtPosts post = agtPostsMapper.selectAgtPostsById(postId);
            if (post != null && StringUtils.isNotEmpty(post.getImage()) && !imageUrls.contains(post.getImage())) {
                imageUrls.add(post.getImage());
            }
            
            logger.info("获取帖子图片成功 - 帖子ID: {}, 图片数量: {}", postId, imageUrls.size());
            return imageUrls;
        } catch (Exception e) {
            logger.error("获取帖子图片异常 - 帖子ID: {}", postId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取帖子评论
     *
     * @param postId 帖子ID
     * @return 评论列表
     */
    private List<AgtPostDetailVO.AgtCommentVO> getPostComments(Long postId) {
        logger.info("获取帖子评论 - 帖子ID: {}", postId);
        List<AgtPostDetailVO.AgtCommentVO> commentVOList = new ArrayList<>();
        
        try {
            // 1. 从评论表获取所有评论
            if (agtPostCommentsMapper != null) {
                // 使用自定义查询方法，包含用户信息
                List<AgtPostComments> commentsList = agtPostCommentsMapper.selectCommentsByPostId(postId);
                
                if (commentsList != null && !commentsList.isEmpty()) {
                    // 2. 转换为VO对象
                    Map<Long, AgtPostDetailVO.AgtCommentVO> commentMap = new HashMap<>();
                    List<AgtPostDetailVO.AgtCommentVO> rootComments = new ArrayList<>();
                    
                    for (AgtPostComments comment : commentsList) {
                        AgtPostDetailVO.AgtCommentVO commentVO = new AgtPostDetailVO.AgtCommentVO();
                        commentVO.setId(comment.getId());
                        commentVO.setPostId(comment.getPostId());
                        commentVO.setUserId(comment.getUserId());
                        commentVO.setUsername(comment.getUsername());
                        commentVO.setUserAvatar(comment.getUserAvatar());
                        commentVO.setContent(comment.getPostContent());
                        commentVO.setParentId(comment.getParentCommentId());
                        commentVO.setCreateTime(comment.getCreatedAt());
                        commentVO.setChildren(new ArrayList<>());
                        
                        // 将评论放入Map中，方便构建树形结构
                        commentMap.put(comment.getId(), commentVO);
                        
                        // 如果是根评论，则加入根评论列表
                        if (comment.getParentCommentId() == null || comment.getParentCommentId() == 0) {
                            rootComments.add(commentVO);
                        }
                    }
                    
                    // 3. 构建评论树形结构
                    for (AgtPostComments comment : commentsList) {
                        if (comment.getParentCommentId() != null && comment.getParentCommentId() != 0) {
                            AgtPostDetailVO.AgtCommentVO parentComment = commentMap.get(comment.getParentCommentId());
                            if (parentComment != null) {
                                parentComment.getChildren().add(commentMap.get(comment.getId()));
                            }
                        }
                    }
                    
                    commentVOList = rootComments;
                }
            } else {
                // 如果Mapper未注入，则尝试从客户端获取数据
                logger.info("AgtPostCommentsMapper未注入，尝试从客户端获取评论数据");
                // 这里可以调用客户端的评论服务，但为避免冲突，我们先使用空实现
            }
            
            logger.info("获取帖子评论成功 - 帖子ID: {}, 评论数量: {}", postId, commentVOList.size());
            return commentVOList;
        } catch (Exception e) {
            logger.error("获取帖子评论异常 - 帖子ID: {}", postId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 更新帖子标签
     *
     * @param postDetailEdit 帖子详情编辑对象
     */
    private void updatePostTags(AgtPostDetailEdit postDetailEdit) {
        Long postId = postDetailEdit.getId();
        logger.info("更新帖子标签 - 帖子ID: {}", postId);
        
        try {
            if (agtPostTagsMapper != null && agtTagsMapper != null) {
                // 1. 删除原有标签关联
                LambdaQueryWrapper<AgtPostTags> deleteWrapper = new LambdaQueryWrapper<>();
                deleteWrapper.eq(AgtPostTags::getPostId, postId);
                agtPostTagsMapper.delete(deleteWrapper);
                
                // 2. 处理新标签
                List<Long> tagIds = postDetailEdit.getTagIds();
                List<String> tagNames = postDetailEdit.getTagNames();
                
                // 如果有标签ID，直接使用
                if (tagIds != null && !tagIds.isEmpty()) {
                    for (Long tagId : tagIds) {
                        AgtPostTags postTag = new AgtPostTags();
                        postTag.setPostId(postId);
                        postTag.setTagId(tagId);
                        agtPostTagsMapper.insert(postTag);
                    }
                }
                // 如果有标签名称，先查找或创建标签，再关联
                else if (tagNames != null && !tagNames.isEmpty()) {
                    for (String tagName : tagNames) {
                        if (StringUtils.isEmpty(tagName)) {
                            continue;
                        }
                        
                        // 查找标签是否存在
                        LambdaQueryWrapper<AgtTags> tagQueryWrapper = new LambdaQueryWrapper<>();
                        tagQueryWrapper.eq(AgtTags::getName, tagName);
                        AgtTags tag = agtTagsMapper.selectOne(tagQueryWrapper);
                        
                        // 如果标签不存在，则创建
                        if (tag == null) {
                            tag = new AgtTags();
                            tag.setName(tagName);
                            agtTagsMapper.insert(tag);
                        }
                        
                        // 创建关联
                        AgtPostTags postTag = new AgtPostTags();
                        postTag.setPostId(postId);
                        postTag.setTagId(tag.getId());
                        agtPostTagsMapper.insert(postTag);
                    }
                }
            } else {
                logger.warn("AgtPostTagsMapper或AgtTagsMapper未注入，无法更新帖子标签");
            }
        } catch (Exception e) {
            logger.error("更新帖子标签异常 - 帖子ID: {}", postId, e);
            throw e; // 抛出异常以触发事务回滚
        }
    }
    
    /**
     * 更新帖子图片
     *
     * @param postDetailEdit 帖子详情编辑对象
     */
    private void updatePostImages(AgtPostDetailEdit postDetailEdit) {
        Long postId = postDetailEdit.getId();
        logger.info("更新帖子图片 - 帖子ID: {}", postId);
        
        try {
            if (agtPostImagesMapper != null) {
                // 1. 删除原有图片关联
                LambdaQueryWrapper<AgtPostImages> deleteWrapper = new LambdaQueryWrapper<>();
                deleteWrapper.eq(AgtPostImages::getPostId, postId);
                agtPostImagesMapper.delete(deleteWrapper);
                
                // 2. 添加新图片
                List<String> imageUrls = postDetailEdit.getImageUrls();
                if (imageUrls != null && !imageUrls.isEmpty()) {
                    for (String imageUrl : imageUrls) {
                        if (StringUtils.isEmpty(imageUrl)) {
                            continue;
                        }
                        
                        AgtPostImages postImage = new AgtPostImages();
                        postImage.setPostId(postId);
                        postImage.setImageUrl(imageUrl);
                        postImage.setCreatedAt(new Date());
                        agtPostImagesMapper.insert(postImage);
                    }
                }
                
                // 3. 更新帖子主图
                if (imageUrls != null && !imageUrls.isEmpty() && StringUtils.isNotEmpty(imageUrls.get(0))) {
                    AgtPosts post = new AgtPosts();
                    post.setId(postId);
                    post.setImage(imageUrls.get(0));
                    agtPostsMapper.updateAgtPosts(post);
                }
            } else {
                logger.warn("AgtPostImagesMapper未注入，无法更新帖子图片");
            }
        } catch (Exception e) {
            logger.error("更新帖子图片异常 - 帖子ID: {}", postId, e);
            throw e; // 抛出异常以触发事务回滚
        }
    }

    /**
     * 插入帖子标签关联
     *
     * @param postId 帖子ID
     * @param tagNames 标签名称列表
     */
    private void insertPostTags(Long postId, List<String> tagNames) {
        logger.info("处理帖子标签 - 帖子ID: {}, 标签数量: {}", postId, tagNames.size());
        
        try {
            if (agtPostTagsMapper != null && agtTagsMapper != null) {
                for (String tagName : tagNames) {
                    if (StringUtils.isEmpty(tagName)) {
                        continue;
                    }
                    
                    // 1. 查找标签是否存在
                    LambdaQueryWrapper<AgtTags> tagQueryWrapper = new LambdaQueryWrapper<>();
                    tagQueryWrapper.eq(AgtTags::getName, tagName);
                    AgtTags tag = agtTagsMapper.selectOne(tagQueryWrapper);
                    
                    // 2. 如果标签不存在，则创建
                    if (tag == null) {
                        tag = new AgtTags();
                        tag.setName(tagName);
                        tag.setCreateTime(DateUtils.getNowDate());
                        agtTagsMapper.insert(tag);
                        logger.info("创建新标签 - 标签ID: {}, 标签名: {}", tag.getId(), tagName);
                    }
                    
                    // 3. 创建帖子标签关联
                    AgtPostTags postTag = new AgtPostTags();
                    postTag.setPostId(postId);
                    postTag.setTagId(tag.getId());
//                    postTag.setCreateTime(DateUtils.getNowDate());
                    agtPostTagsMapper.insert(postTag);
                    
                    logger.info("创建帖子标签关联 - 帖子ID: {}, 标签ID: {}", postId, tag.getId());
                }
            } else {
                logger.warn("标签相关Mapper未注入，无法处理标签");
            }
        } catch (Exception e) {
            logger.error("处理帖子标签异常 - 帖子ID: {}", postId, e);
            throw e;
        }
    }

    /**
     * 插入帖子图片关联
     *
     * @param postId 帖子ID
     * @param imageUrls 图片URL数组
     */
    private void insertPostImages(Long postId, String[] imageUrls) {
        logger.info("处理帖子图片 - 帖子ID: {}, 图片数量: {}", postId, imageUrls.length);
        
        try {
            if (agtPostImagesMapper != null) {
                for (String imageUrl : imageUrls) {
                    if (StringUtils.isEmpty(imageUrl)) {
                        continue;
                    }
                    
                    AgtPostImages postImage = new AgtPostImages();
                    postImage.setPostId(postId);
                    postImage.setImageUrl(imageUrl);
                    postImage.setCreatedAt(new Date());
                    agtPostImagesMapper.insert(postImage);
                    
                    logger.info("创建帖子图片关联 - 帖子ID: {}, 图片URL: {}", postId, imageUrl);
                }
            } else {
                logger.warn("图片Mapper未注入，无法处理图片");
            }
        } catch (Exception e) {
            logger.error("处理帖子图片异常 - 帖子ID: {}", postId, e);
            throw e;
        }
    }

    /**
     * 创建帖子（包含标签和图片处理）
     * @param requestData 请求数据
     * @return 创建结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createPost(Map<String, Object> requestData) {
        logger.info("开始创建帖子 - 请求数据: {}", requestData);

        try {
            // 1. 创建AgtPosts对象
            AgtPosts agtPosts = new AgtPosts();

            // 2. 设置基本字段
            agtPosts.setTitle((String) requestData.get("title"));
            agtPosts.setContent((String) requestData.get("content"));
            agtPosts.setUserId(Long.valueOf(requestData.get("userId").toString()));

            // 3. 处理标签逻辑
            List<String> tagNames = processTagsFromRequest(requestData.get("tags"));
            agtPosts.setTags(tagNames);

            // 4. 处理图片逻辑
            String[] imageUrls = processImagesFromRequest(requestData.get("imageUrls"));
            agtPosts.setImageUrls(imageUrls);

            logger.info("处理后的帖子数据 - 标题: {}, 标签: {}, 图片数量: {}",
                agtPosts.getTitle(), tagNames, imageUrls != null ? imageUrls.length : 0);

            // 5. 保存到数据库
            return insertAgtPosts(agtPosts);

        } catch (Exception e) {
            logger.error("创建帖子失败", e);
            throw new RuntimeException("创建帖子失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理标签数据
     * @param tagsObj 标签对象
     * @return 标签名称列表
     */
    private List<String> processTagsFromRequest(Object tagsObj) {
        List<String> tagNames = new ArrayList<>();

        if (tagsObj instanceof List) {
            List<?> tagsList = (List<?>) tagsObj;

            for (Object tagObj : tagsList) {
                if (tagObj instanceof Map) {
                    // 前端传递的是对象格式 {id: xx, name: "标签名"}
                    Map<String, Object> tagMap = (Map<String, Object>) tagObj;
                    String tagName = (String) tagMap.get("name");
                    if (tagName != null && !tagName.isEmpty()) {
                        tagNames.add(tagName);
                    }
                } else if (tagObj instanceof String) {
                    // 前端传递的是字符串格式
                    String tagName = (String) tagObj;
                    if (tagName != null && !tagName.isEmpty()) {
                        tagNames.add(tagName);
                    }
                }
            }
        }

        logger.info("处理标签完成 - 标签数量: {}, 标签列表: {}", tagNames.size(), tagNames);
        return tagNames;
    }

    /**
     * 处理图片数据
     * @param imageUrlsObj 图片对象
     * @return 图片URL数组
     */
    private String[] processImagesFromRequest(Object imageUrlsObj) {
        if (imageUrlsObj instanceof List) {
            List<String> imageUrlsList = (List<String>) imageUrlsObj;
            String[] imageUrls = imageUrlsList.toArray(new String[0]);
            logger.info("处理图片完成 - 图片数量: {}", imageUrls.length);
            return imageUrls;
        }

        logger.info("未找到图片数据");
        return new String[0];
    }
}
