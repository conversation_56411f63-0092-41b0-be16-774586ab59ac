package com.ylkj.service.impl.omg;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.ylkj.common.enums.VideoTypeEnum;
import com.ylkj.mapper.omg.OmgHomeVideoClientMapper;
import com.ylkj.model.domain.OmgHomeVideoClient;
import com.ylkj.model.vo.OmgHomeVideoClientVO;
import com.ylkj.service.omg.IOmgHomeVideoClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * omg首页视频Service实现类（客户端）
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class OmgHomeVideoClientServiceImpl extends ServiceImpl<OmgHomeVideoClientMapper, OmgHomeVideoClient> implements IOmgHomeVideoClientService {
    
    private static final Logger logger = LoggerFactory.getLogger(OmgHomeVideoClientServiceImpl.class);
    
    /**
     * 获取首页视频列表
     *
     * @return 首页视频VO列表
     */
    @Override
    public List<OmgHomeVideoClientVO> getHomeVideoList() {
        try {
            logger.info("开始查询首页视频列表");
            
            // 查询数据库获取视频列表
            List<OmgHomeVideoClient> videoList = baseMapper.selectOmgHomeVideoClientList();
            
            // 转换为VO对象
            List<OmgHomeVideoClientVO> voList = new ArrayList<>();
            for (OmgHomeVideoClient video : videoList) {
                OmgHomeVideoClientVO vo = new OmgHomeVideoClientVO();
                BeanUtils.copyProperties(video, vo);
                voList.add(vo);
            }
            
            logger.info("查询首页视频列表成功，共{}条数据", voList.size());
            return voList;
        } catch (Exception e) {
            logger.error("查询首页视频列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据类型获取首页视频列表
     *
     * @param type 视频类型字符串
     * @return 首页视频VO列表
     */
    @Override
    public List<OmgHomeVideoClientVO> getHomeVideoListByType(String type) {
        try {
            logger.info("开始查询首页视频列表，类型：{}", type);

            // 根据传入的字符串获取枚举
            VideoTypeEnum videoType = VideoTypeEnum.getByCode(type);
            if (videoType == null) {
                logger.warn("无效的视频类型参数：{}", type);
                return new ArrayList<>();
            }

            // 查询数据库获取指定类型的视频列表
            List<OmgHomeVideoClient> videoList = baseMapper.selectOmgHomeVideoClientListByType(videoType);

            // 转换为VO对象
            List<OmgHomeVideoClientVO> voList = new ArrayList<>();
            for (OmgHomeVideoClient video : videoList) {
                OmgHomeVideoClientVO vo = new OmgHomeVideoClientVO();
                BeanUtils.copyProperties(video, vo);
                voList.add(vo);
            }

            logger.info("查询首页视频列表成功，类型：{}，共{}条数据", videoType.getDesc(), voList.size());
            return voList;
        } catch (Exception e) {
            logger.error("查询首页视频列表失败，类型：{}", type, e);
            return new ArrayList<>();
        }
    }
} 