package com.ylkj.system.service;

import java.util.List;
import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.model.vo.agtProducts.UpdateStatusVo;
import com.ylkj.system.model.vo.omgProducts.OmgProductsVo;
import com.ylkj.system.model.vo.omgProducts.ImportResultVo;
import com.ylkj.system.model.dto.omgProducts.OmgProductsQuery;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
/**
 * omg_商品Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IOmgProductsService extends IService<OmgProducts>
{
    /**
     * 查询omg_商品
     * 
     * @param productId omg_商品主键
     * @return omg_商品
     */
    public OmgProducts selectOmgProductsByProductId(Long productId);

    /**
     * 查询omg_商品列表
     * 
     * @param omgProducts omg_商品
     * @return omg_商品集合
     */
    public List<OmgProducts> selectOmgProductsList(OmgProducts omgProducts);

    /**
     * 新增omg_商品
     * 
     * @param omgProducts omg_商品
     * @return 结果
     */
    public int insertOmgProducts(OmgProducts omgProducts);

    /**
     * 修改omg_商品
     * 
     * @param omgProducts omg_商品
     * @return 结果
     */
    public int updateOmgProducts(OmgProducts omgProducts);

    /**
     * 批量删除omg_商品
     * 
     * @param productIds 需要删除的omg_商品主键集合
     * @return 结果
     */
    public int deleteOmgProductsByProductIds(Long[] productIds);

    /**
     * 删除omg_商品信息
     * 
     * @param productId omg_商品主键
     * @return 结果
     */
    public int deleteOmgProductsByProductId(Long productId);
    //endregion
    /**
     * 获取查询条件
     *
     * @param omgProductsQuery 查询条件对象
     * @return 查询条件
     */
    QueryWrapper<OmgProducts> getQueryWrapper(OmgProductsQuery omgProductsQuery);

    /**
     * 转换vo
     *
     * @param omgProductsList OmgProducts集合
     * @return OmgProductsVO集合
     */
    List<OmgProductsVo> convertVoList(List<OmgProducts> omgProductsList);


    /**
     * @author: 小许
     * @date: 2025/5/13/周二 15:07
     * @description: 更新omg_商品状态
     * @param vo
     * @return int
     */
    public int updateOmgProductsStatus(UpdateStatusVo vo);

    /**
     * @author: 小许
     * @date: 2025/5/13/周二 15:07
     * @description: 导入omg_商品（已优化）
     *
     * <p>性能优化说明：</p>
     * <ul>
     *   <li>统一使用批量处理优化分类和品牌处理</li>
     *   <li>分类处理：从N次单独SQL优化为2次批量SQL（INSERT IGNORE + 批量查询）</li>
     *   <li>品牌处理：从N次单独SQL优化为2次批量SQL（INSERT IGNORE + 批量查询）</li>
     *   <li>性能提升：相比原有逐个处理方式，分类和品牌处理性能提升50-100倍</li>
     * </ul>
     *
     * @param omgProductsList 商品列表
     * @return ImportResultVo 导入结果详情
     */
    ImportResultVo importOmgProducts(List<OmgProducts> omgProductsList);

    /**
     * @author: 系统
     * @date: 2025/7/16
     * @description: 更新淘宝商品链接
     * @return 更新结果数量
     */
    int updateTaobaoProductsUrl();

    /**
     * @author: 系统
     * @date: 2025/7/16
     * @description: 更新1688商品链接
     * @return 更新结果数量
     */
    int update1688ProductsUrl();

    /**
     * @author: 系统
     * @date: 2025/7/16
     * @description: 更新微店商品链接
     * @return 更新结果数量
     */
    int updateWeidianProductsUrl();

    /**
     * 根据SKU查询商品
     *
     * @param sku 商品SKU
     * @return 商品信息
     */
    OmgProducts selectOmgProductsBySku(String sku);

    /**
     * 获取所有商品的SKU列表
     *
     * @return SKU列表
     */
    List<String> getAllProductSkus();

    /**
     * 检查SKU是否已存在
     *
     * @param sku 商品SKU
     * @return true-存在，false-不存在
     */
    boolean isSkuExists(String sku);

    /**
     * 批量检查SKU是否存在
     *
     * @param skuList SKU列表
     * @return 存在的SKU列表
     */
    List<String> checkSkusExist(List<String> skuList);

    /**
     * 大数据量商品导入（优化版本）
     *
     * @param omgProductsList 商品列表
     * @param batchSize 批处理大小，建议500-1000
     * @return 导入结果详情
     */
    ImportResultVo importOmgProductsBatch(List<OmgProducts> omgProductsList, int batchSize);

    /**
     * 获取所有商品的SKU和平台信息
     *
     * @return 商品列表（包含SKU和平台信息）
     */
    List<OmgProducts> selectAllProductsWithSkuAndPlatform();

    /**
     * 根据平台获取商品列表
     *
     * @param platform 平台类型
     * @return 商品列表
     */
    List<OmgProducts> selectProductsByPlatform(String platform);
    /**
     * 获取所有商品
     *
     * @param omgProducts 查询条件
     * @return 商品列表
     */
    List<OmgProducts> getAll(OmgProducts omgProducts);
}
