package com.ylkj.common.model;

import java.util.List;

/**
 * 商品验证警告
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class ProductValidationWarning {
    
    /** 商品在列表中的索引 */
    private int index;
    
    /** 商品标识（如SKU） */
    private String identifier;
    
    /** 警告信息列表 */
    private List<String> warnings;
    
    /** 警告发生时间 */
    private long timestamp;
    
    /** 警告级别 */
    private WarningLevel level;
    
    /**
     * 警告级别枚举
     */
    public enum WarningLevel {
        INFO("信息"),
        LOW("低"),
        MEDIUM("中"),
        HIGH("高");
        
        private final String description;
        
        WarningLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public ProductValidationWarning() {
        this.timestamp = System.currentTimeMillis();
        this.level = WarningLevel.LOW;
    }
    
    public ProductValidationWarning(int index, String identifier, List<String> warnings) {
        this();
        this.index = index;
        this.identifier = identifier;
        this.warnings = warnings;
        this.level = determineWarningLevel(warnings);
    }
    
    public ProductValidationWarning(int index, String identifier, List<String> warnings, WarningLevel level) {
        this();
        this.index = index;
        this.identifier = identifier;
        this.warnings = warnings;
        this.level = level;
    }
    
    /**
     * 根据警告信息确定警告级别
     * 
     * @param warnings 警告信息列表
     * @return 警告级别
     */
    private WarningLevel determineWarningLevel(List<String> warnings) {
        if (warnings == null || warnings.isEmpty()) {
            return WarningLevel.INFO;
        }
        
        // 根据警告内容判断级别
        for (String warning : warnings) {
            String lowerWarning = warning.toLowerCase();
            
            // 高级别警告关键词
            if (lowerWarning.contains("建议") || lowerWarning.contains("推荐") || 
                lowerWarning.contains("可能") || lowerWarning.contains("注意")) {
                return WarningLevel.HIGH;
            }
            
            // 中等级别警告关键词
            if (lowerWarning.contains("过短") || lowerWarning.contains("过长") || 
                lowerWarning.contains("格式") || lowerWarning.contains("不规范")) {
                return WarningLevel.MEDIUM;
            }
        }
        
        return WarningLevel.LOW;
    }
    
    /**
     * 获取警告摘要
     * 
     * @return 警告摘要
     */
    public String getWarningSummary() {
        if (warnings == null || warnings.isEmpty()) {
            return "无警告信息";
        }
        
        if (warnings.size() == 1) {
            return warnings.get(0);
        }
        
        return warnings.get(0) + " 等 " + warnings.size() + " 个警告";
    }
    
    /**
     * 获取所有警告信息的字符串表示
     * 
     * @param separator 分隔符
     * @return 警告信息字符串
     */
    public String getWarningsAsString(String separator) {
        if (warnings == null || warnings.isEmpty()) {
            return "";
        }
        return String.join(separator, warnings);
    }
    
    /**
     * 是否为重要警告
     * 
     * @return true-重要警告，false-一般警告
     */
    public boolean isImportant() {
        return level == WarningLevel.HIGH || level == WarningLevel.MEDIUM;
    }
    
    /**
     * 获取警告数量
     * 
     * @return 警告数量
     */
    public int getWarningCount() {
        return warnings != null ? warnings.size() : 0;
    }
    
    /**
     * 添加警告信息
     * 
     * @param warning 警告信息
     */
    public void addWarning(String warning) {
        if (warnings == null) {
            warnings = new java.util.ArrayList<>();
        }
        warnings.add(warning);
        // 重新确定警告级别
        this.level = determineWarningLevel(warnings);
    }
    
    // Getter and Setter methods
    public int getIndex() {
        return index;
    }
    
    public void setIndex(int index) {
        this.index = index;
    }
    
    public String getIdentifier() {
        return identifier;
    }
    
    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }
    
    public List<String> getWarnings() {
        return warnings;
    }
    
    public void setWarnings(List<String> warnings) {
        this.warnings = warnings;
        // 重新确定警告级别
        this.level = determineWarningLevel(warnings);
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public WarningLevel getLevel() {
        return level;
    }
    
    public void setLevel(WarningLevel level) {
        this.level = level;
    }
    
    @Override
    public String toString() {
        return "ProductValidationWarning{" +
                "index=" + index +
                ", identifier='" + identifier + '\'' +
                ", warningCount=" + getWarningCount() +
                ", level=" + level.getDescription() +
                ", summary='" + getWarningSummary() + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        ProductValidationWarning that = (ProductValidationWarning) o;
        
        if (index != that.index) return false;
        return identifier != null ? identifier.equals(that.identifier) : that.identifier == null;
    }
    
    @Override
    public int hashCode() {
        int result = index;
        result = 31 * result + (identifier != null ? identifier.hashCode() : 0);
        return result;
    }
}
