package com.ylkj.web.controller.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SKU字符串请求对象
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@ApiModel(value = "SKU字符串请求对象", description = "用于传递SKU字符串的请求参数")
public class SkuStringRequest {

    @ApiModelProperty(value = "SKU字符串（多个SKU用逗号、分号或空格分隔）", required = true, example = "SKU001,SKU002,SKU003")
    private String skus;
}
