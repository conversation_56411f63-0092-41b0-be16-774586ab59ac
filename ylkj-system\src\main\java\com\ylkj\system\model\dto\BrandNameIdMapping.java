package com.ylkj.system.model.dto;

/**
 * 品牌名称到ID的映射DTO
 * 用于批量查询品牌ID时的结果映射
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public class BrandNameIdMapping {
    
    /** 品牌名称 */
    private String brandName;
    
    /** 分类ID */
    private Long categoryId;
    
    /** 品牌ID */
    private Long brandId;
    
    /**
     * 默认构造函数
     */
    public BrandNameIdMapping() {
    }
    
    /**
     * 带参构造函数
     * 
     * @param brandName 品牌名称
     * @param categoryId 分类ID
     * @param brandId 品牌ID
     */
    public BrandNameIdMapping(String brandName, Long categoryId, Long brandId) {
        this.brandName = brandName;
        this.categoryId = categoryId;
        this.brandId = brandId;
    }
    
    /**
     * 获取品牌名称
     * 
     * @return 品牌名称
     */
    public String getBrandName() {
        return brandName;
    }
    
    /**
     * 设置品牌名称
     * 
     * @param brandName 品牌名称
     */
    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }
    
    /**
     * 获取分类ID
     * 
     * @return 分类ID
     */
    public Long getCategoryId() {
        return categoryId;
    }
    
    /**
     * 设置分类ID
     * 
     * @param categoryId 分类ID
     */
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    
    /**
     * 获取品牌ID
     * 
     * @return 品牌ID
     */
    public Long getBrandId() {
        return brandId;
    }
    
    /**
     * 设置品牌ID
     * 
     * @param brandId 品牌ID
     */
    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }
    
    /**
     * 生成品牌的唯一键（品牌名称+分类ID）
     * 用于在Map中作为key使用
     * 
     * @return 品牌唯一键
     */
    public String getBrandKey() {
        return brandName + "_" + categoryId;
    }
    
    @Override
    public String toString() {
        return "BrandNameIdMapping{" +
                "brandName='" + brandName + '\'' +
                ", categoryId=" + categoryId +
                ", brandId=" + brandId +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        BrandNameIdMapping that = (BrandNameIdMapping) o;
        
        if (brandName != null ? !brandName.equals(that.brandName) : that.brandName != null) return false;
        if (categoryId != null ? !categoryId.equals(that.categoryId) : that.categoryId != null) return false;
        return brandId != null ? brandId.equals(that.brandId) : that.brandId == null;
    }
    
    @Override
    public int hashCode() {
        int result = brandName != null ? brandName.hashCode() : 0;
        result = 31 * result + (categoryId != null ? categoryId.hashCode() : 0);
        result = 31 * result + (brandId != null ? brandId.hashCode() : 0);
        return result;
    }
}
