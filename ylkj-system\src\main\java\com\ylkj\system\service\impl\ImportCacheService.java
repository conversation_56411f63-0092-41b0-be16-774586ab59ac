package com.ylkj.system.service.impl;

import com.ylkj.common.service.CacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 导入过程中的缓存服务
 * 用于缓存分类、品牌、SKU等信息，减少数据库查询
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class ImportCacheService {

    @Autowired
    private CacheService cacheService;

    // 缓存容器名称
    private static final String CATEGORY_CONTAINER = "import_category";
    private static final String BRAND_CONTAINER = "import_brand";
    private static final String SKU_CONTAINER = "import_sku";
    private static final String BATCH_CHECKED_CONTAINER = "import_batch_checked";
    
    /**
     * 获取分类ID
     */
    public Long getCategoryId(String categoryName) {
        return cacheService.get(CATEGORY_CONTAINER, categoryName, Long.class);
    }

    /**
     * 缓存分类ID
     */
    public void putCategoryId(String categoryName, Long categoryId) {
        cacheService.put(CATEGORY_CONTAINER, categoryName, categoryId);
    }
    
    /**
     * 获取品牌ID
     */
    public Long getBrandId(String brandName, Long categoryId) {
        String key = brandName + "_" + categoryId;
        return cacheService.get(BRAND_CONTAINER, key, Long.class);
    }

    /**
     * 缓存品牌ID
     */
    public void putBrandId(String brandName, Long categoryId, Long brandId) {
        if (brandName != null && categoryId != null && brandId != null) {
            String key = brandName + "_" + categoryId;
            cacheService.put(BRAND_CONTAINER, key, brandId);
        }
    }
    
    /**
     * 检查SKU是否存在（从缓存）
     */
    public Boolean isSkuExists(String sku) {
        return cacheService.get(SKU_CONTAINER, sku, Boolean.class);
    }

    /**
     * 缓存SKU存在性
     */
    public void putSkuExists(String sku, boolean exists) {
        if (sku != null) {
            cacheService.put(SKU_CONTAINER, sku, exists);
        }
    }
    
    /**
     * 批量缓存SKU存在性
     */
    public void putSkuExistsBatch(Set<String> existingSkus, Set<String> allSkus) {
        for (String sku : allSkus) {
            putSkuExists(sku, existingSkus.contains(sku));
        }
        // 将批量检查的SKU添加到已检查集合
        Set<String> batchChecked = getBatchCheckedSkus();
        batchChecked.addAll(allSkus);
        cacheService.put(BATCH_CHECKED_CONTAINER, "checked_skus", batchChecked);
    }

    /**
     * 检查SKU是否已经被批量检查过
     */
    public boolean isBatchChecked(String sku) {
        Set<String> batchChecked = getBatchCheckedSkus();
        return batchChecked.contains(sku);
    }

    /**
     * 获取已批量检查的SKU集合
     */
    @SuppressWarnings("unchecked")
    private Set<String> getBatchCheckedSkus() {
        Set<String> batchChecked = cacheService.get(BATCH_CHECKED_CONTAINER, "checked_skus", Set.class);
        if (batchChecked == null) {
            batchChecked = new HashSet<>();
            cacheService.put(BATCH_CHECKED_CONTAINER, "checked_skus", batchChecked);
        }
        return batchChecked;
    }
    
    /**
     * 清理所有缓存
     */
    public void clearAll() {
        cacheService.clear(CATEGORY_CONTAINER);
        cacheService.clear(BRAND_CONTAINER);
        cacheService.clear(SKU_CONTAINER);
        cacheService.clear(BATCH_CHECKED_CONTAINER);
    }

    /**
     * 清理SKU相关缓存
     */
    public void clearSkuCache() {
        cacheService.clear(SKU_CONTAINER);
        cacheService.clear(BATCH_CHECKED_CONTAINER);
    }
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, Integer> getCacheStats() {
        Map<String, Integer> stats = new ConcurrentHashMap<>();
        stats.put("categoryCache", cacheService.size(CATEGORY_CONTAINER));
        stats.put("brandCache", cacheService.size(BRAND_CONTAINER));
        stats.put("skuExistsCache", cacheService.size(SKU_CONTAINER));
        stats.put("batchCheckedSkus", getBatchCheckedSkus().size());
        return stats;
    }
    
    /**
     * 预热缓存（可选）
     * 在大批量导入前预先加载常用的分类和品牌信息
     */
    public void warmUpCache() {
        // 可以在这里预加载常用的分类和品牌信息
        // 例如：加载最近使用的分类和品牌
    }
}
