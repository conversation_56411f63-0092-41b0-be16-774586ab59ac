package com.ylkj.model.domain;

import java.io.Serializable;
import java.util.Map;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ylkj.common.enums.VideoTypeEnum;
import lombok.Data;

/**
 * omg首页视频实体类（客户端）
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@TableName("omg_home_video")
@Data
public class OmgHomeVideoClient implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 达人视频表id */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 视频URL */
    private String videoUrl;

    /** 头像 */
    private String avatar;

    /** 名称 */
    private String username;

    /** 内容 */
    private String description;

    /** 视频类型 */
    private VideoTypeEnum type;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;
} 